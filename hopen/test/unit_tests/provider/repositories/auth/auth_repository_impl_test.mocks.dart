// Mocks generated by <PERSON>cki<PERSON> 5.4.5 from annotations
// in hopen/test/unit_tests/provider/repositories/auth/auth_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:hopen/provider/services/auth/ory_auth_service.dart' as _i3;
import 'package:hopen/provider/services/http3_client_service.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeHttp3ClientService_0 extends _i1.SmartFake
    implements _i2.Http3ClientService {
  _FakeHttp3ClientService_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeOryAuthResponse_1 extends _i1.SmartFake
    implements _i3.OryAuthResponse {
  _FakeOryAuthResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [OryAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockOryAuthService extends _i1.Mock implements _i3.OryAuthService {
  MockOryAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i3.OryAuthState> get authStateStream => (super.noSuchMethod(
        Invocation.getter(#authStateStream),
        returnValue: _i4.Stream<_i3.OryAuthState>.empty(),
      ) as _i4.Stream<_i3.OryAuthState>);

  @override
  _i4.Stream<_i3.OryUser?> get userStream => (super.noSuchMethod(
        Invocation.getter(#userStream),
        returnValue: _i4.Stream<_i3.OryUser?>.empty(),
      ) as _i4.Stream<_i3.OryUser?>);

  @override
  bool get isSignedIn => (super.noSuchMethod(
        Invocation.getter(#isSignedIn),
        returnValue: false,
      ) as bool);

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<_i2.Http3ClientService> get httpClient => (super.noSuchMethod(
        Invocation.getter(#httpClient),
        returnValue:
            _i4.Future<_i2.Http3ClientService>.value(_FakeHttp3ClientService_0(
          this,
          Invocation.getter(#httpClient),
        )),
      ) as _i4.Future<_i2.Http3ClientService>);

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i3.OryAuthResponse> signUpWithEmail({
    required String? email,
    required String? password,
    String? firstName,
    String? lastName,
    String? username,
    DateTime? dateOfBirth,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUpWithEmail,
          [],
          {
            #email: email,
            #password: password,
            #firstName: firstName,
            #lastName: lastName,
            #username: username,
            #dateOfBirth: dateOfBirth,
            #metadata: metadata,
          },
        ),
        returnValue:
            _i4.Future<_i3.OryAuthResponse>.value(_FakeOryAuthResponse_1(
          this,
          Invocation.method(
            #signUpWithEmail,
            [],
            {
              #email: email,
              #password: password,
              #firstName: firstName,
              #lastName: lastName,
              #username: username,
              #dateOfBirth: dateOfBirth,
              #metadata: metadata,
            },
          ),
        )),
      ) as _i4.Future<_i3.OryAuthResponse>);

  @override
  _i4.Future<_i3.OryAuthResponse> signInWithEmail({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmail,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue:
            _i4.Future<_i3.OryAuthResponse>.value(_FakeOryAuthResponse_1(
          this,
          Invocation.method(
            #signInWithEmail,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i4.Future<_i3.OryAuthResponse>);

  @override
  _i4.Future<_i3.OryAuthResponse> signInWithGoogle() => (super.noSuchMethod(
        Invocation.method(
          #signInWithGoogle,
          [],
        ),
        returnValue:
            _i4.Future<_i3.OryAuthResponse>.value(_FakeOryAuthResponse_1(
          this,
          Invocation.method(
            #signInWithGoogle,
            [],
          ),
        )),
      ) as _i4.Future<_i3.OryAuthResponse>);

  @override
  _i4.Future<_i3.OryAuthResponse> signInWithApple() => (super.noSuchMethod(
        Invocation.method(
          #signInWithApple,
          [],
        ),
        returnValue:
            _i4.Future<_i3.OryAuthResponse>.value(_FakeOryAuthResponse_1(
          this,
          Invocation.method(
            #signInWithApple,
            [],
          ),
        )),
      ) as _i4.Future<_i3.OryAuthResponse>);

  @override
  _i4.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> refreshUserData() => (super.noSuchMethod(
        Invocation.method(
          #refreshUserData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<void> debugTokenStorage() => (super.noSuchMethod(
        Invocation.method(
          #debugTokenStorage,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> storeSessionToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #storeSessionToken,
          [token],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> hasValidToken() => (super.noSuchMethod(
        Invocation.method(
          #hasValidToken,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<String?> getValidToken() => (super.noSuchMethod(
        Invocation.method(
          #getValidToken,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<_i3.OryAuthResponse> initiatePasswordReset(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #initiatePasswordReset,
          [email],
        ),
        returnValue:
            _i4.Future<_i3.OryAuthResponse>.value(_FakeOryAuthResponse_1(
          this,
          Invocation.method(
            #initiatePasswordReset,
            [email],
          ),
        )),
      ) as _i4.Future<_i3.OryAuthResponse>);
}
