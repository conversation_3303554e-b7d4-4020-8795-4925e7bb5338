// Mocks generated by Mocki<PERSON> 5.4.5 from annotations
// in hopen/test/unit_tests/mock_generation.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:io' as _i17;

import 'package:event_bus/event_bus.dart' as _i18;
import 'package:hopen/provider/services/auth/ory_auth_service.dart' as _i4;
import 'package:hopen/provider/services/http3_client_service.dart' as _i3;
import 'package:hopen/repositories/active_bubble/active_bubble_repository.dart'
    as _i11;
import 'package:hopen/repositories/auth/auth_repository.dart' as _i2;
import 'package:hopen/repositories/bubble/bubble_repository.dart' as _i13;
import 'package:hopen/statefulbusinesslogic/core/error/result.dart' as _i7;
import 'package:hopen/statefulbusinesslogic/core/models/bubble_entity.dart'
    as _i12;
import 'package:hopen/statefulbusinesslogic/core/models/bubble_request_model.dart'
    as _i15;
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart' as _i8;
import 'package:hopen/statefulbusinesslogic/core/models/value_objects.dart'
    as _i14;
import 'package:hopen/statefulbusinesslogic/core/usecases/login_usecase.dart'
    as _i10;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:mqtt5_client/mqtt5_client.dart' as _i5;
import 'package:mqtt5_client/mqtt5_server_client.dart' as _i16;
import 'package:typed_data/typed_data.dart' as _i19;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthRepository_0 extends _i1.SmartFake
    implements _i2.AuthRepository {
  _FakeAuthRepository_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeHttp3ClientService_1 extends _i1.SmartFake
    implements _i3.Http3ClientService {
  _FakeHttp3ClientService_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeOryAuthResponse_2 extends _i1.SmartFake
    implements _i4.OryAuthResponse {
  _FakeOryAuthResponse_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMqttDisconnectMessage_3 extends _i1.SmartFake
    implements _i5.MqttDisconnectMessage {
  _FakeMqttDisconnectMessage_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMqttConnectMessage_4 extends _i1.SmartFake
    implements _i5.MqttConnectMessage {
  _FakeMqttConnectMessage_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMqttAuthenticateMessage_5 extends _i1.SmartFake
    implements _i5.MqttAuthenticateMessage {
  _FakeMqttAuthenticateMessage_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i2.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  _i6.Future<_i7.Result<_i8.UserModel>> login({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i8.UserModel>>.value(
            _i9.dummyValue<_i7.Result<_i8.UserModel>>(
          this,
          Invocation.method(
            #login,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i8.UserModel>>);

  @override
  _i6.Future<_i7.Result<_i8.UserModel>> loginWithGoogle() =>
      (super.noSuchMethod(
        Invocation.method(
          #loginWithGoogle,
          [],
        ),
        returnValue: _i6.Future<_i7.Result<_i8.UserModel>>.value(
            _i9.dummyValue<_i7.Result<_i8.UserModel>>(
          this,
          Invocation.method(
            #loginWithGoogle,
            [],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i8.UserModel>>);

  @override
  _i6.Future<_i7.Result<_i8.UserModel>> loginWithApple() => (super.noSuchMethod(
        Invocation.method(
          #loginWithApple,
          [],
        ),
        returnValue: _i6.Future<_i7.Result<_i8.UserModel>>.value(
            _i9.dummyValue<_i7.Result<_i8.UserModel>>(
          this,
          Invocation.method(
            #loginWithApple,
            [],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i8.UserModel>>);

  @override
  _i6.Future<_i7.Result<_i8.UserModel>> signUp({
    required String? email,
    required String? password,
    required String? username,
    required String? firstName,
    required String? lastName,
    DateTime? birthday,
    String? profilePictureUrl,
    bool? notificationsEnabled = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUp,
          [],
          {
            #email: email,
            #password: password,
            #username: username,
            #firstName: firstName,
            #lastName: lastName,
            #birthday: birthday,
            #profilePictureUrl: profilePictureUrl,
            #notificationsEnabled: notificationsEnabled,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i8.UserModel>>.value(
            _i9.dummyValue<_i7.Result<_i8.UserModel>>(
          this,
          Invocation.method(
            #signUp,
            [],
            {
              #email: email,
              #password: password,
              #username: username,
              #firstName: firstName,
              #lastName: lastName,
              #birthday: birthday,
              #profilePictureUrl: profilePictureUrl,
              #notificationsEnabled: notificationsEnabled,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i8.UserModel>>);

  @override
  _i6.Future<_i7.Result<_i8.UserModel>> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i6.Future<_i7.Result<_i8.UserModel>>.value(
            _i9.dummyValue<_i7.Result<_i8.UserModel>>(
          this,
          Invocation.method(
            #getCurrentUser,
            [],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i8.UserModel>>);

  @override
  _i6.Future<_i7.Result<bool>> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue:
            _i6.Future<_i7.Result<bool>>.value(_i9.dummyValue<_i7.Result<bool>>(
          this,
          Invocation.method(
            #logout,
            [],
          ),
        )),
      ) as _i6.Future<_i7.Result<bool>>);

  @override
  _i6.Future<_i7.Result<bool>> updateOnboardingStatus(
          bool? hasCompletedOnboarding) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateOnboardingStatus,
          [hasCompletedOnboarding],
        ),
        returnValue:
            _i6.Future<_i7.Result<bool>>.value(_i9.dummyValue<_i7.Result<bool>>(
          this,
          Invocation.method(
            #updateOnboardingStatus,
            [hasCompletedOnboarding],
          ),
        )),
      ) as _i6.Future<_i7.Result<bool>>);
}

/// A class which mocks [LoginUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginUseCase extends _i1.Mock implements _i10.LoginUseCase {
  MockLoginUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i6.Future<_i7.Result<_i8.UserModel>> call(_i10.LoginParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i6.Future<_i7.Result<_i8.UserModel>>.value(
            _i9.dummyValue<_i7.Result<_i8.UserModel>>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i8.UserModel>>);
}

/// A class which mocks [OryAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockOryAuthService extends _i1.Mock implements _i4.OryAuthService {
  MockOryAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Stream<_i4.OryAuthState> get authStateStream => (super.noSuchMethod(
        Invocation.getter(#authStateStream),
        returnValue: _i6.Stream<_i4.OryAuthState>.empty(),
      ) as _i6.Stream<_i4.OryAuthState>);

  @override
  _i6.Stream<_i4.OryUser?> get userStream => (super.noSuchMethod(
        Invocation.getter(#userStream),
        returnValue: _i6.Stream<_i4.OryUser?>.empty(),
      ) as _i6.Stream<_i4.OryUser?>);

  @override
  bool get isSignedIn => (super.noSuchMethod(
        Invocation.getter(#isSignedIn),
        returnValue: false,
      ) as bool);

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  _i6.Future<_i3.Http3ClientService> get httpClient => (super.noSuchMethod(
        Invocation.getter(#httpClient),
        returnValue:
            _i6.Future<_i3.Http3ClientService>.value(_FakeHttp3ClientService_1(
          this,
          Invocation.getter(#httpClient),
        )),
      ) as _i6.Future<_i3.Http3ClientService>);

  @override
  _i6.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<_i4.OryAuthResponse> signUpWithEmail({
    required String? email,
    required String? password,
    String? firstName,
    String? lastName,
    String? username,
    DateTime? dateOfBirth,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUpWithEmail,
          [],
          {
            #email: email,
            #password: password,
            #firstName: firstName,
            #lastName: lastName,
            #username: username,
            #dateOfBirth: dateOfBirth,
            #metadata: metadata,
          },
        ),
        returnValue:
            _i6.Future<_i4.OryAuthResponse>.value(_FakeOryAuthResponse_2(
          this,
          Invocation.method(
            #signUpWithEmail,
            [],
            {
              #email: email,
              #password: password,
              #firstName: firstName,
              #lastName: lastName,
              #username: username,
              #dateOfBirth: dateOfBirth,
              #metadata: metadata,
            },
          ),
        )),
      ) as _i6.Future<_i4.OryAuthResponse>);

  @override
  _i6.Future<_i4.OryAuthResponse> signInWithEmail({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmail,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue:
            _i6.Future<_i4.OryAuthResponse>.value(_FakeOryAuthResponse_2(
          this,
          Invocation.method(
            #signInWithEmail,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i6.Future<_i4.OryAuthResponse>);

  @override
  _i6.Future<_i4.OryAuthResponse> signInWithGoogle() => (super.noSuchMethod(
        Invocation.method(
          #signInWithGoogle,
          [],
        ),
        returnValue:
            _i6.Future<_i4.OryAuthResponse>.value(_FakeOryAuthResponse_2(
          this,
          Invocation.method(
            #signInWithGoogle,
            [],
          ),
        )),
      ) as _i6.Future<_i4.OryAuthResponse>);

  @override
  _i6.Future<_i4.OryAuthResponse> signInWithApple() => (super.noSuchMethod(
        Invocation.method(
          #signInWithApple,
          [],
        ),
        returnValue:
            _i6.Future<_i4.OryAuthResponse>.value(_FakeOryAuthResponse_2(
          this,
          Invocation.method(
            #signInWithApple,
            [],
          ),
        )),
      ) as _i6.Future<_i4.OryAuthResponse>);

  @override
  _i6.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> refreshUserData() => (super.noSuchMethod(
        Invocation.method(
          #refreshUserData,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<void> debugTokenStorage() => (super.noSuchMethod(
        Invocation.method(
          #debugTokenStorage,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> storeSessionToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #storeSessionToken,
          [token],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> hasValidToken() => (super.noSuchMethod(
        Invocation.method(
          #hasValidToken,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<String?> getValidToken() => (super.noSuchMethod(
        Invocation.method(
          #getValidToken,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<_i4.OryAuthResponse> initiatePasswordReset(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #initiatePasswordReset,
          [email],
        ),
        returnValue:
            _i6.Future<_i4.OryAuthResponse>.value(_FakeOryAuthResponse_2(
          this,
          Invocation.method(
            #initiatePasswordReset,
            [email],
          ),
        )),
      ) as _i6.Future<_i4.OryAuthResponse>);
}

/// A class which mocks [ActiveBubbleRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockActiveBubbleRepository extends _i1.Mock
    implements _i11.ActiveBubbleRepository {
  MockActiveBubbleRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity?>> getActiveBubble(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getActiveBubble,
          [userId],
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity?>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity?>>(
          this,
          Invocation.method(
            #getActiveBubble,
            [userId],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity?>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> updateBubbleStatus(
    String? bubbleId,
    _i12.BubbleLifecycleStatus? status,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubbleStatus,
          [
            bubbleId,
            status,
          ],
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #updateBubbleStatus,
            [
              bubbleId,
              status,
            ],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Stream<_i12.BubbleEntity> getBubbleUpdates(String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleUpdates,
          [bubbleId],
        ),
        returnValue: _i6.Stream<_i12.BubbleEntity>.empty(),
      ) as _i6.Stream<_i12.BubbleEntity>);

  @override
  _i6.Future<_i7.Result<void>> updateMemberOnlineStatus(
    String? bubbleId,
    String? userId,
    bool? isOnline,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMemberOnlineStatus,
          [
            bubbleId,
            userId,
            isOnline,
          ],
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #updateMemberOnlineStatus,
            [
              bubbleId,
              userId,
              isOnline,
            ],
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<int>> getOnlineMembersCount(String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getOnlineMembersCount,
          [bubbleId],
        ),
        returnValue:
            _i6.Future<_i7.Result<int>>.value(_i9.dummyValue<_i7.Result<int>>(
          this,
          Invocation.method(
            #getOnlineMembersCount,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<int>>);

  @override
  _i6.Future<_i7.Result<List<Map<String, dynamic>>>> getBubbleActivity(
          String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleActivity,
          [bubbleId],
        ),
        returnValue: _i6.Future<_i7.Result<List<Map<String, dynamic>>>>.value(
            _i9.dummyValue<_i7.Result<List<Map<String, dynamic>>>>(
          this,
          Invocation.method(
            #getBubbleActivity,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<List<Map<String, dynamic>>>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> updateBubbleCountdown(
    String? bubbleId,
    DateTime? newEndTime,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubbleCountdown,
          [
            bubbleId,
            newEndTime,
          ],
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #updateBubbleCountdown,
            [
              bubbleId,
              newEndTime,
            ],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> extendBubbleDuration(
    String? bubbleId,
    Duration? extension,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #extendBubbleDuration,
          [
            bubbleId,
            extension,
          ],
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #extendBubbleDuration,
            [
              bubbleId,
              extension,
            ],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<Duration?>> getBubbleTimeRemaining(String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleTimeRemaining,
          [bubbleId],
        ),
        returnValue: _i6.Future<_i7.Result<Duration?>>.value(
            _i9.dummyValue<_i7.Result<Duration?>>(
          this,
          Invocation.method(
            #getBubbleTimeRemaining,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<Duration?>>);

  @override
  _i6.Future<_i7.Result<bool>> isBubbleAboutToExpire(
    String? bubbleId, {
    Duration? threshold = const Duration(minutes: 30),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #isBubbleAboutToExpire,
          [bubbleId],
          {#threshold: threshold},
        ),
        returnValue:
            _i6.Future<_i7.Result<bool>>.value(_i9.dummyValue<_i7.Result<bool>>(
          this,
          Invocation.method(
            #isBubbleAboutToExpire,
            [bubbleId],
            {#threshold: threshold},
          ),
        )),
      ) as _i6.Future<_i7.Result<bool>>);

  @override
  _i6.Future<_i7.Result<Map<String, bool>>> getMemberActivityStatus(
          String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMemberActivityStatus,
          [bubbleId],
        ),
        returnValue: _i6.Future<_i7.Result<Map<String, bool>>>.value(
            _i9.dummyValue<_i7.Result<Map<String, bool>>>(
          this,
          Invocation.method(
            #getMemberActivityStatus,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<Map<String, bool>>>);

  @override
  _i6.Future<_i7.Result<void>> updateMemberLastSeen(
    String? bubbleId,
    String? userId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMemberLastSeen,
          [
            bubbleId,
            userId,
          ],
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #updateMemberLastSeen,
            [
              bubbleId,
              userId,
            ],
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<Map<String, dynamic>>> getBubbleEngagementMetrics(
          String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleEngagementMetrics,
          [bubbleId],
        ),
        returnValue: _i6.Future<_i7.Result<Map<String, dynamic>>>.value(
            _i9.dummyValue<_i7.Result<Map<String, dynamic>>>(
          this,
          Invocation.method(
            #getBubbleEngagementMetrics,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<Map<String, dynamic>>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity?>> refreshActiveBubble(
          String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #refreshActiveBubble,
          [userId],
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity?>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity?>>(
          this,
          Invocation.method(
            #refreshActiveBubble,
            [userId],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity?>>);
}

/// A class which mocks [BubbleRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBubbleRepository extends _i1.Mock implements _i13.BubbleRepository {
  MockBubbleRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> createBubble({
    required _i14.BubbleName? name,
    required String? description,
    String? locationName,
    double? locationLat,
    double? locationLng,
    int? locationRadius,
    String? customImageUrl,
    String? colorTheme,
    bool? allowInvites,
    bool? requireApproval,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createBubble,
          [],
          {
            #name: name,
            #description: description,
            #locationName: locationName,
            #locationLat: locationLat,
            #locationLng: locationLng,
            #locationRadius: locationRadius,
            #customImageUrl: customImageUrl,
            #colorTheme: colorTheme,
            #allowInvites: allowInvites,
            #requireApproval: requireApproval,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #createBubble,
            [],
            {
              #name: name,
              #description: description,
              #locationName: locationName,
              #locationLat: locationLat,
              #locationLng: locationLng,
              #locationRadius: locationRadius,
              #customImageUrl: customImageUrl,
              #colorTheme: colorTheme,
              #allowInvites: allowInvites,
              #requireApproval: requireApproval,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> getBubble(
          _i14.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubble,
          [bubbleId],
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #getBubble,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> getBubbleDetailsById(
          _i14.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleDetailsById,
          [bubbleId],
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #getBubbleDetailsById,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<void>> updateBubble(
    _i14.BubbleId? bubbleId,
    Map<String, dynamic>? updates,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubble,
          [
            bubbleId,
            updates,
          ],
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #updateBubble,
            [
              bubbleId,
              updates,
            ],
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<void>> deleteBubble(_i14.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteBubble,
          [bubbleId],
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #deleteBubble,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> joinBubble({
    required _i14.BubbleId? bubbleId,
    _i14.UserId? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #joinBubble,
          [],
          {
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #joinBubble,
            [],
            {
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<void>> leaveBubble({
    required _i14.BubbleId? bubbleId,
    _i14.UserId? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #leaveBubble,
          [],
          {
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #leaveBubble,
            [],
            {
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<List<_i8.UserModel>>> getBubbleMembers(
          _i14.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleMembers,
          [bubbleId],
        ),
        returnValue: _i6.Future<_i7.Result<List<_i8.UserModel>>>.value(
            _i9.dummyValue<_i7.Result<List<_i8.UserModel>>>(
          this,
          Invocation.method(
            #getBubbleMembers,
            [bubbleId],
          ),
        )),
      ) as _i6.Future<_i7.Result<List<_i8.UserModel>>>);

  @override
  _i6.Future<_i7.Result<List<_i12.BubbleEntity>>> getUserBubbles(
          _i14.UserId? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserBubbles,
          [userId],
        ),
        returnValue: _i6.Future<_i7.Result<List<_i12.BubbleEntity>>>.value(
            _i9.dummyValue<_i7.Result<List<_i12.BubbleEntity>>>(
          this,
          Invocation.method(
            #getUserBubbles,
            [userId],
          ),
        )),
      ) as _i6.Future<_i7.Result<List<_i12.BubbleEntity>>>);

  @override
  _i6.Future<_i7.Result<List<_i12.BubbleEntity>>> getBubbles(
          _i14.UserId? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbles,
          [userId],
        ),
        returnValue: _i6.Future<_i7.Result<List<_i12.BubbleEntity>>>.value(
            _i9.dummyValue<_i7.Result<List<_i12.BubbleEntity>>>(
          this,
          Invocation.method(
            #getBubbles,
            [userId],
          ),
        )),
      ) as _i6.Future<_i7.Result<List<_i12.BubbleEntity>>>);

  @override
  _i6.Future<_i7.Result<List<_i12.BubbleEntity>>> getNearbyBubbles({
    required double? latitude,
    required double? longitude,
    required double? radius,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNearbyBubbles,
          [],
          {
            #latitude: latitude,
            #longitude: longitude,
            #radius: radius,
          },
        ),
        returnValue: _i6.Future<_i7.Result<List<_i12.BubbleEntity>>>.value(
            _i9.dummyValue<_i7.Result<List<_i12.BubbleEntity>>>(
          this,
          Invocation.method(
            #getNearbyBubbles,
            [],
            {
              #latitude: latitude,
              #longitude: longitude,
              #radius: radius,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<List<_i12.BubbleEntity>>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> updateMemberStatus({
    required _i14.BubbleId? bubbleId,
    required _i14.UserId? memberId,
    bool? isOnline,
    int? unreadMessageCount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMemberStatus,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
            #isOnline: isOnline,
            #unreadMessageCount: unreadMessageCount,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #updateMemberStatus,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
              #isOnline: isOnline,
              #unreadMessageCount: unreadMessageCount,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<void>> inviteToBubble({
    required _i14.BubbleId? bubbleId,
    required _i14.UserId? inviterId,
    required List<_i14.UserId>? inviteeIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #inviteToBubble,
          [],
          {
            #bubbleId: bubbleId,
            #inviterId: inviterId,
            #inviteeIds: inviteeIds,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #inviteToBubble,
            [],
            {
              #bubbleId: bubbleId,
              #inviterId: inviterId,
              #inviteeIds: inviteeIds,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> voteToRemoveMember({
    required _i14.BubbleId? bubbleId,
    required _i14.UserId? voterId,
    required _i14.UserId? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #voteToRemoveMember,
          [],
          {
            #bubbleId: bubbleId,
            #voterId: voterId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #voteToRemoveMember,
            [],
            {
              #bubbleId: bubbleId,
              #voterId: voterId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<void>> removeFromBubble({
    required _i14.BubbleId? bubbleId,
    required _i14.UserId? memberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeFromBubble,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #removeFromBubble,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<_i12.BubbleEntity>> updateBubbleInfo({
    required _i14.BubbleId? bubbleId,
    _i14.BubbleName? name,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubbleInfo,
          [],
          {
            #bubbleId: bubbleId,
            #name: name,
            #endDate: endDate,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i12.BubbleEntity>>.value(
            _i9.dummyValue<_i7.Result<_i12.BubbleEntity>>(
          this,
          Invocation.method(
            #updateBubbleInfo,
            [],
            {
              #bubbleId: bubbleId,
              #name: name,
              #endDate: endDate,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i12.BubbleEntity>>);

  @override
  _i6.Future<_i7.Result<void>> markAllMessagesRead({
    required _i14.BubbleId? bubbleId,
    required _i14.UserId? memberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #markAllMessagesRead,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #markAllMessagesRead,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<void>> proposeMember({
    required _i14.BubbleId? bubbleId,
    required String? memberName,
    required String? memberEmail,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #proposeMember,
          [],
          {
            #bubbleId: bubbleId,
            #memberName: memberName,
            #memberEmail: memberEmail,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #proposeMember,
            [],
            {
              #bubbleId: bubbleId,
              #memberName: memberName,
              #memberEmail: memberEmail,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<void>> startCall({
    required _i14.BubbleId? bubbleId,
    required String? callId,
    required List<_i14.UserId>? participants,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #startCall,
          [],
          {
            #bubbleId: bubbleId,
            #callId: callId,
            #participants: participants,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #startCall,
            [],
            {
              #bubbleId: bubbleId,
              #callId: callId,
              #participants: participants,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<void>> endCall({
    required _i14.BubbleId? bubbleId,
    required String? callId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #endCall,
          [],
          {
            #bubbleId: bubbleId,
            #callId: callId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #endCall,
            [],
            {
              #bubbleId: bubbleId,
              #callId: callId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<_i15.BubbleRequestModel>> createRequest({
    required String? bubbleId,
    required String? targetId,
    required String? type,
    String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createRequest,
          [],
          {
            #bubbleId: bubbleId,
            #targetId: targetId,
            #type: type,
            #message: message,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i15.BubbleRequestModel>>.value(
            _i9.dummyValue<_i7.Result<_i15.BubbleRequestModel>>(
          this,
          Invocation.method(
            #createRequest,
            [],
            {
              #bubbleId: bubbleId,
              #targetId: targetId,
              #type: type,
              #message: message,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i15.BubbleRequestModel>>);

  @override
  _i6.Future<_i7.Result<_i15.BubbleRequestModel>> respondToRequest({
    required String? requestId,
    required String? status,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #respondToRequest,
          [],
          {
            #requestId: requestId,
            #status: status,
          },
        ),
        returnValue: _i6.Future<_i7.Result<_i15.BubbleRequestModel>>.value(
            _i9.dummyValue<_i7.Result<_i15.BubbleRequestModel>>(
          this,
          Invocation.method(
            #respondToRequest,
            [],
            {
              #requestId: requestId,
              #status: status,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<_i15.BubbleRequestModel>>);

  @override
  _i6.Future<_i7.Result<List<_i15.BubbleRequestModel>>> getPendingRequests() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingRequests,
          [],
        ),
        returnValue:
            _i6.Future<_i7.Result<List<_i15.BubbleRequestModel>>>.value(
                _i9.dummyValue<_i7.Result<List<_i15.BubbleRequestModel>>>(
          this,
          Invocation.method(
            #getPendingRequests,
            [],
          ),
        )),
      ) as _i6.Future<_i7.Result<List<_i15.BubbleRequestModel>>>);

  @override
  _i6.Future<void> selectFriends({
    required String? bubbleId,
    required List<String>? selectedUserIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectFriends,
          [],
          {
            #bubbleId: bubbleId,
            #selectedUserIds: selectedUserIds,
          },
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<_i7.Result<void>> acceptBubbleRequest({
    required String? requestId,
    required String? bubbleId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptBubbleRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #acceptBubbleRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<void>> declineBubbleRequest({
    required String? requestId,
    required String? bubbleId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineBubbleRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #declineBubbleRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<void>> acceptBubbleKickoutRequest({
    required String? requestId,
    required String? bubbleId,
    required String? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptBubbleKickoutRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #acceptBubbleKickoutRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<void>> declineBubbleKickoutRequest({
    required String? requestId,
    required String? bubbleId,
    required String? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineBubbleKickoutRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue:
            _i6.Future<_i7.Result<void>>.value(_i9.dummyValue<_i7.Result<void>>(
          this,
          Invocation.method(
            #declineBubbleKickoutRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i6.Future<_i7.Result<void>>);
}

/// A class which mocks [MqttServerClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockMqttServerClient extends _i1.Mock implements _i16.MqttServerClient {
  MockMqttServerClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i17.SecurityContext get securityContext => (super.noSuchMethod(
        Invocation.getter(#securityContext),
        returnValue: _i9.dummyValue<_i17.SecurityContext>(
          this,
          Invocation.getter(#securityContext),
        ),
      ) as _i17.SecurityContext);

  @override
  set securityContext(_i17.SecurityContext? _securityContext) =>
      super.noSuchMethod(
        Invocation.setter(
          #securityContext,
          _securityContext,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onBadCertificate(bool Function(dynamic)? _onBadCertificate) =>
      super.noSuchMethod(
        Invocation.setter(
          #onBadCertificate,
          _onBadCertificate,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get useWebSocket => (super.noSuchMethod(
        Invocation.getter(#useWebSocket),
        returnValue: false,
      ) as bool);

  @override
  set useWebSocket(bool? _useWebSocket) => super.noSuchMethod(
        Invocation.setter(
          #useWebSocket,
          _useWebSocket,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get useAlternateWebSocketImplementation => (super.noSuchMethod(
        Invocation.getter(#useAlternateWebSocketImplementation),
        returnValue: false,
      ) as bool);

  @override
  set useAlternateWebSocketImplementation(
          bool? _useAlternateWebSocketImplementation) =>
      super.noSuchMethod(
        Invocation.setter(
          #useAlternateWebSocketImplementation,
          _useAlternateWebSocketImplementation,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get secure => (super.noSuchMethod(
        Invocation.getter(#secure),
        returnValue: false,
      ) as bool);

  @override
  set secure(bool? _secure) => super.noSuchMethod(
        Invocation.setter(
          #secure,
          _secure,
        ),
        returnValueForMissingStub: null,
      );

  @override
  int get maxConnectionAttempts => (super.noSuchMethod(
        Invocation.getter(#maxConnectionAttempts),
        returnValue: 0,
      ) as int);

  @override
  List<_i17.RawSocketOption> get socketOptions => (super.noSuchMethod(
        Invocation.getter(#socketOptions),
        returnValue: <_i17.RawSocketOption>[],
      ) as List<_i17.RawSocketOption>);

  @override
  set socketOptions(List<_i17.RawSocketOption>? _socketOptions) =>
      super.noSuchMethod(
        Invocation.setter(
          #socketOptions,
          _socketOptions,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set socketTimeout(int? period) => super.noSuchMethod(
        Invocation.setter(
          #socketTimeout,
          period,
        ),
        returnValueForMissingStub: null,
      );

  @override
  String get server => (super.noSuchMethod(
        Invocation.getter(#server),
        returnValue: _i9.dummyValue<String>(
          this,
          Invocation.getter(#server),
        ),
      ) as String);

  @override
  set server(String? _server) => super.noSuchMethod(
        Invocation.setter(
          #server,
          _server,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set port(int? _port) => super.noSuchMethod(
        Invocation.setter(
          #port,
          _port,
        ),
        returnValueForMissingStub: null,
      );

  @override
  String get clientIdentifier => (super.noSuchMethod(
        Invocation.getter(#clientIdentifier),
        returnValue: _i9.dummyValue<String>(
          this,
          Invocation.getter(#clientIdentifier),
        ),
      ) as String);

  @override
  set clientIdentifier(String? _clientIdentifier) => super.noSuchMethod(
        Invocation.setter(
          #clientIdentifier,
          _clientIdentifier,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get instantiationCorrect => (super.noSuchMethod(
        Invocation.getter(#instantiationCorrect),
        returnValue: false,
      ) as bool);

  @override
  set instantiationCorrect(bool? _instantiationCorrect) => super.noSuchMethod(
        Invocation.setter(
          #instantiationCorrect,
          _instantiationCorrect,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get autoReconnect => (super.noSuchMethod(
        Invocation.getter(#autoReconnect),
        returnValue: false,
      ) as bool);

  @override
  set autoReconnect(bool? _autoReconnect) => super.noSuchMethod(
        Invocation.setter(
          #autoReconnect,
          _autoReconnect,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get resubscribeOnAutoReconnect => (super.noSuchMethod(
        Invocation.getter(#resubscribeOnAutoReconnect),
        returnValue: false,
      ) as bool);

  @override
  set resubscribeOnAutoReconnect(bool? _resubscribeOnAutoReconnect) =>
      super.noSuchMethod(
        Invocation.setter(
          #resubscribeOnAutoReconnect,
          _resubscribeOnAutoReconnect,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set connectionHandler(dynamic _connectionHandler) => super.noSuchMethod(
        Invocation.setter(
          #connectionHandler,
          _connectionHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set websocketProtocolString(List<String>? _websocketProtocolString) =>
      super.noSuchMethod(
        Invocation.setter(
          #websocketProtocolString,
          _websocketProtocolString,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set subscriptionsManager(
          _i5.MqttSubscriptionManager? _subscriptionsManager) =>
      super.noSuchMethod(
        Invocation.setter(
          #subscriptionsManager,
          _subscriptionsManager,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set keepAlive(_i5.MqttConnectionKeepAlive? _keepAlive) => super.noSuchMethod(
        Invocation.setter(
          #keepAlive,
          _keepAlive,
        ),
        returnValueForMissingStub: null,
      );

  @override
  int get keepAlivePeriod => (super.noSuchMethod(
        Invocation.getter(#keepAlivePeriod),
        returnValue: 0,
      ) as int);

  @override
  set keepAlivePeriod(int? _keepAlivePeriod) => super.noSuchMethod(
        Invocation.setter(
          #keepAlivePeriod,
          _keepAlivePeriod,
        ),
        returnValueForMissingStub: null,
      );

  @override
  int get disconnectOnNoResponsePeriod => (super.noSuchMethod(
        Invocation.getter(#disconnectOnNoResponsePeriod),
        returnValue: 0,
      ) as int);

  @override
  set disconnectOnNoResponsePeriod(int? _disconnectOnNoResponsePeriod) =>
      super.noSuchMethod(
        Invocation.setter(
          #disconnectOnNoResponsePeriod,
          _disconnectOnNoResponsePeriod,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set publishingManager(_i5.MqttPublishingManager? _publishingManager) =>
      super.noSuchMethod(
        Invocation.setter(
          #publishingManager,
          _publishingManager,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set authenticationManager(
          _i5.MqttAuthenticationManager? _authenticationManager) =>
      super.noSuchMethod(
        Invocation.setter(
          #authenticationManager,
          _authenticationManager,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set connectionMessage(_i5.MqttConnectMessage? _connectionMessage) =>
      super.noSuchMethod(
        Invocation.setter(
          #connectionMessage,
          _connectionMessage,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.MqttDisconnectMessage get disconnectMessage => (super.noSuchMethod(
        Invocation.getter(#disconnectMessage),
        returnValue: _FakeMqttDisconnectMessage_3(
          this,
          Invocation.getter(#disconnectMessage),
        ),
      ) as _i5.MqttDisconnectMessage);

  @override
  set disconnectMessage(_i5.MqttDisconnectMessage? _disconnectMessage) =>
      super.noSuchMethod(
        Invocation.setter(
          #disconnectMessage,
          _disconnectMessage,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onDisconnected(_i5.DisconnectCallback? _onDisconnected) =>
      super.noSuchMethod(
        Invocation.setter(
          #onDisconnected,
          _onDisconnected,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onConnected(_i5.ConnectCallback? _onConnected) => super.noSuchMethod(
        Invocation.setter(
          #onConnected,
          _onConnected,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onAutoReconnect(_i5.AutoReconnectCallback? _onAutoReconnect) =>
      super.noSuchMethod(
        Invocation.setter(
          #onAutoReconnect,
          _onAutoReconnect,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onAutoReconnected(
          _i5.AutoReconnectCompleteCallback? _onAutoReconnected) =>
      super.noSuchMethod(
        Invocation.setter(
          #onAutoReconnected,
          _onAutoReconnected,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onFailedConnectionAttempt(
          _i5.FailedConnectionAttemptCallback? _onFailedConnectionAttempt) =>
      super.noSuchMethod(
        Invocation.setter(
          #onFailedConnectionAttempt,
          _onFailedConnectionAttempt,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set clientEventBus(_i18.EventBus? _clientEventBus) => super.noSuchMethod(
        Invocation.setter(
          #clientEventBus,
          _clientEventBus,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set websocketProtocols(List<String>? protocols) => super.noSuchMethod(
        Invocation.setter(
          #websocketProtocols,
          protocols,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onSubscribed(_i5.SubscribeCallback? cb) => super.noSuchMethod(
        Invocation.setter(
          #onSubscribed,
          cb,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onSubscribeFail(_i5.SubscribeFailCallback? cb) => super.noSuchMethod(
        Invocation.setter(
          #onSubscribeFail,
          cb,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onUnsubscribed(_i5.UnsubscribeCallback? cb) => super.noSuchMethod(
        Invocation.setter(
          #onUnsubscribed,
          cb,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set pongCallback(_i5.PongCallback? cb) => super.noSuchMethod(
        Invocation.setter(
          #pongCallback,
          cb,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Stream<List<_i5.MqttReceivedMessage<_i5.MqttMessage>>> get updates =>
      (super.noSuchMethod(
        Invocation.getter(#updates),
        returnValue:
            _i6.Stream<List<_i5.MqttReceivedMessage<_i5.MqttMessage>>>.empty(),
      ) as _i6.Stream<List<_i5.MqttReceivedMessage<_i5.MqttMessage>>>);

  @override
  _i6.Future<_i5.MqttConnectionStatus?> connect([
    String? username,
    String? password,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #connect,
          [
            username,
            password,
          ],
        ),
        returnValue: _i6.Future<_i5.MqttConnectionStatus?>.value(),
      ) as _i6.Future<_i5.MqttConnectionStatus?>);

  @override
  _i5.MqttConnectMessage getConnectMessage(
    String? username,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getConnectMessage,
          [
            username,
            password,
          ],
        ),
        returnValue: _FakeMqttConnectMessage_4(
          this,
          Invocation.method(
            #getConnectMessage,
            [
              username,
              password,
            ],
          ),
        ),
      ) as _i5.MqttConnectMessage);

  @override
  void doAutoReconnect({bool? force = false}) => super.noSuchMethod(
        Invocation.method(
          #doAutoReconnect,
          [],
          {#force: force},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.MqttSubscription? subscribe(
    String? topic,
    _i5.MqttQos? qosLevel,
  ) =>
      (super.noSuchMethod(Invocation.method(
        #subscribe,
        [
          topic,
          qosLevel,
        ],
      )) as _i5.MqttSubscription?);

  @override
  _i5.MqttSubscription? subscribeWithSubscription(
          _i5.MqttSubscription? subscription) =>
      (super.noSuchMethod(Invocation.method(
        #subscribeWithSubscription,
        [subscription],
      )) as _i5.MqttSubscription?);

  @override
  List<_i5.MqttSubscription>? subscribeWithSubscriptionList(
          List<_i5.MqttSubscription>? subscriptions) =>
      (super.noSuchMethod(Invocation.method(
        #subscribeWithSubscriptionList,
        [subscriptions],
      )) as List<_i5.MqttSubscription>?);

  @override
  void resubscribe() => super.noSuchMethod(
        Invocation.method(
          #resubscribe,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  int publishMessage(
    String? topic,
    _i5.MqttQos? qualityOfService,
    _i19.Uint8Buffer? data, {
    bool? retain = false,
    List<_i5.MqttUserProperty>? userProperties,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #publishMessage,
          [
            topic,
            qualityOfService,
            data,
          ],
          {
            #retain: retain,
            #userProperties: userProperties,
          },
        ),
        returnValue: 0,
      ) as int);

  @override
  int publishUserMessage(_i5.MqttPublishMessage? message) =>
      (super.noSuchMethod(
        Invocation.method(
          #publishUserMessage,
          [message],
        ),
        returnValue: 0,
      ) as int);

  @override
  void unsubscribeStringTopic(String? topic) => super.noSuchMethod(
        Invocation.method(
          #unsubscribeStringTopic,
          [topic],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unsubscribeSubscription(_i5.MqttSubscription? subscription) =>
      super.noSuchMethod(
        Invocation.method(
          #unsubscribeSubscription,
          [subscription],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unsubscribeSubscriptionList(List<_i5.MqttSubscription>? subscriptions) =>
      super.noSuchMethod(
        Invocation.method(
          #unsubscribeSubscriptionList,
          [subscriptions],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.MqttSubscriptionStatus getSubscriptionTopicStatus(String? topic) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSubscriptionTopicStatus,
          [topic],
        ),
        returnValue: _i5.MqttSubscriptionStatus.doesNotExist,
      ) as _i5.MqttSubscriptionStatus);

  @override
  _i5.MqttSubscriptionStatus getSubscriptionStatus(
          _i5.MqttSubscription? subscription) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSubscriptionStatus,
          [subscription],
        ),
        returnValue: _i5.MqttSubscriptionStatus.doesNotExist,
      ) as _i5.MqttSubscriptionStatus);

  @override
  void disconnect() => super.noSuchMethod(
        Invocation.method(
          #disconnect,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void disconnectOnNoPingResponse(_i5.DisconnectOnNoPingResponse? event) =>
      super.noSuchMethod(
        Invocation.method(
          #disconnectOnNoPingResponse,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<_i5.MqttAuthenticateMessage> reauthenticate(
    _i5.MqttAuthenticateMessage? msg, {
    int? waitTimeInSeconds = 30,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #reauthenticate,
          [msg],
          {#waitTimeInSeconds: waitTimeInSeconds},
        ),
        returnValue: _i6.Future<_i5.MqttAuthenticateMessage>.value(
            _FakeMqttAuthenticateMessage_5(
          this,
          Invocation.method(
            #reauthenticate,
            [msg],
            {#waitTimeInSeconds: waitTimeInSeconds},
          ),
        )),
      ) as _i6.Future<_i5.MqttAuthenticateMessage>);

  @override
  void sendAuthenticate(_i5.MqttAuthenticateMessage? message) =>
      super.noSuchMethod(
        Invocation.method(
          #sendAuthenticate,
          [message],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void internalDisconnect() => super.noSuchMethod(
        Invocation.method(
          #internalDisconnect,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void checkCredentials(
    String? username,
    String? password,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #checkCredentials,
          [
            username,
            password,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void logging({
    required bool? on,
    bool? logPayloads = true,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #logging,
          [],
          {
            #on: on,
            #logPayloads: logPayloads,
          },
        ),
        returnValueForMissingStub: null,
      );
}
