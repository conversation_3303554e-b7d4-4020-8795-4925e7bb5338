// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in hopen/test/unit_tests/mocks/mocks.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:ui' as _i13;

import 'package:bloc/bloc.dart' as _i9;
import 'package:hopen/repositories/contacts/contacts_repository.dart' as _i3;
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_bloc.dart' as _i6;
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_event.dart' as _i8;
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_state.dart' as _i2;
import 'package:hopen/statefulbusinesslogic/bloc/contacts/contacts_bloc.dart'
    as _i10;
import 'package:hopen/statefulbusinesslogic/bloc/contacts/contacts_event.dart'
    as _i11;
import 'package:hopen/statefulbusinesslogic/bloc/contacts/contacts_state.dart'
    as _i4;
import 'package:hopen/statefulbusinesslogic/bloc/notification/notification_bloc.dart'
    as _i14;
import 'package:hopen/statefulbusinesslogic/bloc/notification/notification_event.dart'
    as _i15;
import 'package:hopen/statefulbusinesslogic/bloc/notification/notification_state.dart'
    as _i5;
import 'package:hopen/statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart'
    as _i12;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthState_0 extends _i1.SmartFake implements _i2.AuthState {
  _FakeAuthState_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeContactsRepository_1 extends _i1.SmartFake
    implements _i3.ContactsRepository {
  _FakeContactsRepository_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeContactsState_2 extends _i1.SmartFake implements _i4.ContactsState {
  _FakeContactsState_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeNotificationState_3 extends _i1.SmartFake
    implements _i5.NotificationState {
  _FakeNotificationState_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthBloc extends _i1.Mock implements _i6.AuthBloc {
  MockAuthBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeAuthState_0(
          this,
          Invocation.getter(#state),
        ),
      ) as _i2.AuthState);

  @override
  _i7.Stream<_i2.AuthState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i7.Stream<_i2.AuthState>.empty(),
      ) as _i7.Stream<_i2.AuthState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  _i7.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  void add(_i8.AuthEvent? event) => super.noSuchMethod(
        Invocation.method(
          #add,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i8.AuthEvent? event) => super.noSuchMethod(
        Invocation.method(
          #onEvent,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i2.AuthState? state) => super.noSuchMethod(
        Invocation.method(
          #emit,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i8.AuthEvent>(
    _i9.EventHandler<E, _i2.AuthState>? handler, {
    _i9.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #on,
          [handler],
          {#transformer: transformer},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(_i9.Transition<_i8.AuthEvent, _i2.AuthState>? transition) =>
      super.noSuchMethod(
        Invocation.method(
          #onTransition,
          [transition],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onChange(_i9.Change<_i2.AuthState>? change) => super.noSuchMethod(
        Invocation.method(
          #onChange,
          [change],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addError(
    Object? error, [
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onError(
    Object? error,
    StackTrace? stackTrace,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #onError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ContactsBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockContactsBloc extends _i1.Mock implements _i10.ContactsBloc {
  MockContactsBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.ContactsRepository get contactsRepository => (super.noSuchMethod(
        Invocation.getter(#contactsRepository),
        returnValue: _FakeContactsRepository_1(
          this,
          Invocation.getter(#contactsRepository),
        ),
      ) as _i3.ContactsRepository);

  @override
  _i4.ContactsState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeContactsState_2(
          this,
          Invocation.getter(#state),
        ),
      ) as _i4.ContactsState);

  @override
  _i7.Stream<_i4.ContactsState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i7.Stream<_i4.ContactsState>.empty(),
      ) as _i7.Stream<_i4.ContactsState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  _i7.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  void add(_i11.ContactsEvent? event) => super.noSuchMethod(
        Invocation.method(
          #add,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i11.ContactsEvent? event) => super.noSuchMethod(
        Invocation.method(
          #onEvent,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i4.ContactsState? state) => super.noSuchMethod(
        Invocation.method(
          #emit,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i11.ContactsEvent>(
    _i9.EventHandler<E, _i4.ContactsState>? handler, {
    _i9.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #on,
          [handler],
          {#transformer: transformer},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(
          _i9.Transition<_i11.ContactsEvent, _i4.ContactsState>? transition) =>
      super.noSuchMethod(
        Invocation.method(
          #onTransition,
          [transition],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onChange(_i9.Change<_i4.ContactsState>? change) => super.noSuchMethod(
        Invocation.method(
          #onChange,
          [change],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addError(
    Object? error, [
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onError(
    Object? error,
    StackTrace? stackTrace,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #onError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [NavBarVisibilityNotifier].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavBarVisibilityNotifier extends _i1.Mock
    implements _i12.NavBarVisibilityNotifier {
  MockNavBarVisibilityNotifier() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isNavBarVisible => (super.noSuchMethod(
        Invocation.getter(#isNavBarVisible),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  void setNavBarVisible(bool? visible) => super.noSuchMethod(
        Invocation.method(
          #setNavBarVisible,
          [visible],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void showNavBar() => super.noSuchMethod(
        Invocation.method(
          #showNavBar,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void hideNavBar() => super.noSuchMethod(
        Invocation.method(
          #hideNavBar,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i13.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i13.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [NotificationBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationBloc extends _i1.Mock implements _i14.NotificationBloc {
  MockNotificationBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.NotificationState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeNotificationState_3(
          this,
          Invocation.getter(#state),
        ),
      ) as _i5.NotificationState);

  @override
  _i7.Stream<_i5.NotificationState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i7.Stream<_i5.NotificationState>.empty(),
      ) as _i7.Stream<_i5.NotificationState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  _i7.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);

  @override
  void add(_i15.NotificationEvent? event) => super.noSuchMethod(
        Invocation.method(
          #add,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i15.NotificationEvent? event) => super.noSuchMethod(
        Invocation.method(
          #onEvent,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i5.NotificationState? state) => super.noSuchMethod(
        Invocation.method(
          #emit,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i15.NotificationEvent>(
    _i9.EventHandler<E, _i5.NotificationState>? handler, {
    _i9.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #on,
          [handler],
          {#transformer: transformer},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(
          _i9.Transition<_i15.NotificationEvent, _i5.NotificationState>?
              transition) =>
      super.noSuchMethod(
        Invocation.method(
          #onTransition,
          [transition],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onChange(_i9.Change<_i5.NotificationState>? change) =>
      super.noSuchMethod(
        Invocation.method(
          #onChange,
          [change],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addError(
    Object? error, [
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onError(
    Object? error,
    StackTrace? stackTrace,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #onError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );
}
