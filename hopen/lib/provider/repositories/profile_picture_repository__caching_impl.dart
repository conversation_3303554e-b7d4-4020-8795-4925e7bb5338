import '../../../repositories/profile_picture/profile_picture_repository.dart';
import '../services/hopen_cache_manager.dart';
import '../services/profile_picture_preloader.dart';
import '../services/profile_picture_performance_monitor.dart';

/// Implementation of ProfilePictureRepository that follows the four-layer dependency rule
/// 
/// This implementation is in the provider layer and depends only on external services
/// and the repository interface from the repository layer.
class ProfilePictureRepositoryCachingImpl implements ProfilePictureRepository {
  final HopenCacheManager _cacheManager;
  final ProfilePicturePreloader _preloader;
  final ProfilePicturePerformanceMonitor _monitor;

  ProfilePictureRepositoryCachingImpl({
    HopenCacheManager? cacheManager,
    ProfilePicturePreloader? preloader,
    ProfilePicturePerformanceMonitor? monitor,
  }) : _cacheManager = cacheManager ?? HopenCacheManager(),
       _preloader = preloader ?? ProfilePicturePreloader(),
       _monitor = monitor ?? ProfilePicturePerformanceMonitor();

  @override
  Future<void> preloadProfilePicture(String imageUrl) async {
    await _preloader.preloadProfilePicture(imageUrl);
  }

  @override
  Future<void> preloadProfilePictures(List<String> imageUrls) async {
    await _preloader.preloadProfilePictures(imageUrls);
  }

  @override
  bool isPreloaded(String imageUrl) {
    return _preloader.isPreloaded(imageUrl);
  }

  @override
  Map<String, dynamic> getPreloadStats() {
    return _preloader.getPreloadStats();
  }

  @override
  void clearPreloadedData() {
    _preloader.clearPreloadedData();
  }

  @override
  Future<Map<String, dynamic>> getCacheStats() async {
    return await _cacheManager.getCacheStats();
  }

  @override
  Future<void> clearCache() async {
    await _cacheManager.clearCacheWithLogging();
  }

  @override
  Future<void> optimizeCache() async {
    await _cacheManager.optimizeForProfilePictures();
  }

  @override
  String recordLoadStart(String imageUrl) {
    return _monitor.recordLoadStart(imageUrl);
  }

  @override
  void recordLoadComplete(String loadId, String imageUrl, {bool fromCache = false}) {
    _monitor.recordLoadComplete(loadId, imageUrl, fromCache: fromCache);
  }

  @override
  void recordLoadError(String loadId, String imageUrl, String error) {
    _monitor.recordLoadError(loadId, imageUrl, error);
  }

  @override
  Map<String, dynamic> getImageStats(String imageUrl) {
    return _monitor.getImageStats(imageUrl);
  }

  @override
  Map<String, dynamic> getOverallStats() {
    return _monitor.getOverallStats();
  }

  @override
  List<String> getPerformanceRecommendations() {
    return _monitor.getPerformanceRecommendations();
  }

  @override
  void clearPerformanceData() {
    _monitor.clearPerformanceData();
  }

  @override
  void dispose() {
    _preloader.dispose();
    _monitor.dispose();
  }
} 