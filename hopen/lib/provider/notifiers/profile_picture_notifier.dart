import 'dart:typed_data';
import 'dart:io';
import 'dart:collection';
import 'package:flutter/material.dart';
import '../../repositories/local_storage/abstract_profile_picture_repository.dart';

/// State management for profile pictures using Provider pattern
///
/// This ChangeNotifier manages a multi-layered cache with LRU optimization:
/// 1. In-Memory LRU Cache (fastest) - Uint8List data in RAM with size limits
/// 2. Disk Cache (persistent) - Managed by Drift database
/// 3. Network (slowest) - Original source via CachedNetworkImage
class ProfilePictureNotifier extends ChangeNotifier {
  final AbstractProfilePictureRepository _repository;

  // LRU cache implementation for better memory management
  final LinkedHashMap<String, Uint8List> _memoryCache = LinkedHashMap();
  static const int _maxCacheSize = 50; // Maximum number of cached images
  static const int _maxMemoryMB = 100; // Maximum memory usage in MB
  final int _maxMemoryBytes = _maxMemoryMB * 1024 * 1024;
  int _currentMemoryBytes = 0;

  // Performance metrics
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _evictions = 0;

  // Loading states per image URL
  final Map<String, bool> _loadingStates = {};

  // Error states per image URL
  final Map<String, String?> _errorStates = {};

  ProfilePictureNotifier(this._repository);

  /// Check if an image is currently loading
  bool isLoading(String imageUrl) => _loadingStates[imageUrl] ?? false;

  /// Get error message for an image URL
  String? getError(String imageUrl) => _errorStates[imageUrl];

  /// Get cached image data from memory with LRU update
  Uint8List? getImageData(String imageUrl) {
    final data = _memoryCache.remove(imageUrl);
    if (data != null) {
      // Move to end (most recently used)
      _memoryCache[imageUrl] = data;
      _cacheHits++;
      debugPrint('🎯 Cache HIT for $imageUrl');
      return data;
    }
    _cacheMisses++;
    debugPrint('❌ Cache MISS for $imageUrl');
    return null;
  }

  /// Check if image is available in memory cache
  bool isImageCached(String imageUrl) => _memoryCache.containsKey(imageUrl);

  /// Fetch profile picture with multi-layered caching
  Future<void> fetchProfilePicture(String userId, String imageUrl) async {
    // Skip if already in memory cache
    if (_memoryCache.containsKey(imageUrl)) {
      debugPrint(
        '🖼️ ProfilePictureNotifier: Image already in memory cache: $imageUrl',
      );
      return;
    }

    // Skip if already loading
    if (_loadingStates[imageUrl] == true) {
      debugPrint(
        '🖼️ ProfilePictureNotifier: Image already loading: $imageUrl',
      );
      return;
    }

    _setLoadingState(imageUrl, true);
    _clearError(imageUrl);

    try {
      debugPrint(
        '🖼️ ProfilePictureNotifier: Fetching profile picture for user $userId',
      );

      // 1. Check disk cache first (Drift database)
      final cachedPath = await _repository.getProfilePicturePath(
        userId,
        imageUrl,
      );

      if (cachedPath != null) {
        debugPrint(
          '🖼️ ProfilePictureNotifier: Found in disk cache: $cachedPath',
        );

        // Load from disk into memory
        final file = File(cachedPath);
        if (await file.exists()) {
          final imageBytes = await file.readAsBytes();
          _putInCache(imageUrl, imageBytes);
          debugPrint(
            '🖼️ ProfilePictureNotifier: Loaded from disk to memory cache',
          );

          // Update last accessed time
          await _repository.updateLastAccessed(userId);
        } else {
          debugPrint(
            '🖼️ ProfilePictureNotifier: Cached file no longer exists: $cachedPath',
          );
          // File was deleted, let it fall through to network fetch
        }
      } else {
        debugPrint(
          '🖼️ ProfilePictureNotifier: Not found in disk cache, will use CachedNetworkImage fallback',
        );
        // Not in disk cache - CachedNetworkImage will handle network fetch and caching
      }
    } catch (e) {
      debugPrint(
        '🖼️ ProfilePictureNotifier: Error fetching profile picture: $e',
      );
      _setError(imageUrl, 'Failed to load profile picture: $e');
    } finally {
      _setLoadingState(imageUrl, false);
    }
  }

  /// Invalidate cache and refetch image
  Future<void> invalidateAndRefetch(String userId, String imageUrl) async {
    debugPrint(
      '🖼️ ProfilePictureNotifier: Invalidating cache for user $userId',
    );

    // 1. Clear memory cache
    _memoryCache.remove(imageUrl);
    _clearError(imageUrl);

    // 2. Clear disk cache
    try {
      // Note: We don't have a direct invalidate method in the repository
      // The repository will handle cache invalidation internally
      await _repository.clearOldCache(Duration.zero); // Clear all old cache
    } catch (e) {
      debugPrint('🖼️ ProfilePictureNotifier: Error clearing disk cache: $e');
    }

    // 3. Refetch
    await fetchProfilePicture(userId, imageUrl);
  }

  /// Clear all caches
  Future<void> clearAllCaches() async {
    debugPrint('🖼️ ProfilePictureNotifier: Clearing all caches');

    // Clear memory cache
    _memoryCache.clear();
    _loadingStates.clear();
    _errorStates.clear();

    // Clear disk cache
    try {
      await _repository.clearAllCache();
    } catch (e) {
      debugPrint('🖼️ ProfilePictureNotifier: Error clearing disk cache: $e');
    }

    notifyListeners();
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    final diskStats = await _repository.getCacheStats();

    return {
      'memoryCache': {
        'totalImages': _memoryCache.length,
        'totalSizeBytes': _memoryCache.values.fold<int>(
          0,
          (sum, bytes) => sum + bytes.length,
        ),
      },
      'diskCache': diskStats,
    };
  }

  /// Private helper methods
  void _setLoadingState(String imageUrl, bool isLoading) {
    _loadingStates[imageUrl] = isLoading;
    notifyListeners();
  }

  void _setError(String imageUrl, String error) {
    _errorStates[imageUrl] = error;
    notifyListeners();
  }

  void _clearError(String imageUrl) {
    _errorStates.remove(imageUrl);
    notifyListeners();
  }

  /// Put image data in LRU cache with size management
  void _putInCache(String key, Uint8List data) {
    final dataSize = data.lengthInBytes;

    // Check if single image exceeds max memory
    if (dataSize > _maxMemoryBytes) {
      debugPrint('🚨 Image too large for cache: ${dataSize / 1024 / 1024}MB');
      return;
    }

    // Remove existing entry if present
    if (_memoryCache.containsKey(key)) {
      final oldData = _memoryCache.remove(key)!;
      _currentMemoryBytes -= oldData.lengthInBytes;
    }

    // Ensure we have space
    _ensureSpace(dataSize);

    // Add new entry
    _memoryCache[key] = data;
    _currentMemoryBytes += dataSize;

    debugPrint('💾 Cached image: $key (${dataSize / 1024}KB)');
  }

  /// Ensure we have space for new data
  void _ensureSpace(int requiredBytes) {
    // Remove old entries until we have space
    while ((_currentMemoryBytes + requiredBytes > _maxMemoryBytes ||
            _memoryCache.length >= _maxCacheSize) &&
        _memoryCache.isNotEmpty) {
      _evictOldest();
    }
  }

  /// Evict the oldest (least recently used) entry
  void _evictOldest() {
    if (_memoryCache.isEmpty) return;

    final oldestKey = _memoryCache.keys.first;
    final oldestData = _memoryCache.remove(oldestKey)!;
    _currentMemoryBytes -= oldestData.lengthInBytes;
    _evictions++;

    debugPrint('🗑️ Evicted from cache: $oldestKey');
  }

  @override
  void dispose() {
    _memoryCache.clear();
    _loadingStates.clear();
    _errorStates.clear();
    super.dispose();
  }
}
