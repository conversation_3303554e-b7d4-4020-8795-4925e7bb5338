import 'dart:async';
import 'package:flutter/foundation.dart';
import 'hopen_cache_manager.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';

/// Profile picture preloader service for improved performance
/// 
/// This service preloads profile pictures in the background to reduce
/// loading times when they're actually displayed in the UI.
class ProfilePicturePreloader {
  factory ProfilePicturePreloader() => _instance;
  ProfilePicturePreloader._internal();
  static final ProfilePicturePreloader _instance = ProfilePicturePreloader._internal();

  final HopenCacheManager _cacheManager = HopenCacheManager();
  final Set<String> _preloadedUrls = {};
  final Map<String, DateTime> _lastPreloadTimes = {};
  final Map<String, Completer<void>> _preloadCompleters = {};
  
  // Configuration
  static const Duration _preloadCooldown = Duration(minutes: 5);
  static const int _maxConcurrentPreloads = 3;
  static const int _maxPreloadedUrls = 50;
  
  bool _isInitialized = false;

  /// Initialize the preloader
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Start background cleanup
      _startBackgroundCleanup();
      
      LoggingService.info('ProfilePicturePreloader initialized');
      _isInitialized = true;
    } catch (e) {
      LoggingService.error('Failed to initialize ProfilePicturePreloader', e);
    }
  }

  /// Preload a single profile picture
  Future<void> preloadProfilePicture(String imageUrl) async {
    if (imageUrl.isEmpty || !imageUrl.startsWith('http')) return;
    
    // Check if already preloaded recently
    if (_isRecentlyPreloaded(imageUrl)) {
      return;
    }
    
    // Check if already preloading
    if (_preloadCompleters.containsKey(imageUrl)) {
      await _preloadCompleters[imageUrl]!.future;
      return;
    }
    
    // Create completer for this preload
    final completer = Completer<void>();
    _preloadCompleters[imageUrl] = completer;
    
    try {
      await _performPreload(imageUrl);
      completer.complete();
    } catch (e) {
      completer.completeError(e);
    } finally {
      _preloadCompleters.remove(imageUrl);
    }
  }

  /// Preload multiple profile pictures efficiently
  Future<void> preloadProfilePictures(List<String> imageUrls) async {
    if (imageUrls.isEmpty) return;
    
    // Filter valid URLs and remove recently preloaded ones
    final validUrls = imageUrls
        .where((url) => url.isNotEmpty && url.startsWith('http'))
        .where((url) => !_isRecentlyPreloaded(url))
        .toList();
    
    if (validUrls.isEmpty) return;
    
    LoggingService.info('Preloading ${validUrls.length} profile pictures');
    
    // Preload in batches to avoid overwhelming the system
    const batchSize = _maxConcurrentPreloads;
    for (int i = 0; i < validUrls.length; i += batchSize) {
      final batch = validUrls.skip(i).take(batchSize).toList();
      
      await Future.wait(
        batch.map((url) => preloadProfilePicture(url).catchError((e) {
          LoggingService.warning('Failed to preload profile picture: $url', e);
        })),
      );
      
      // Small delay between batches
      if (i + batchSize < validUrls.length) {
        await Future.delayed(const Duration(milliseconds: 200));
      }
    }
    
    LoggingService.info('Profile picture preloading completed');
  }

  /// Check if a URL was recently preloaded
  bool _isRecentlyPreloaded(String imageUrl) {
    final lastPreload = _lastPreloadTimes[imageUrl];
    if (lastPreload == null) return false;
    
    return DateTime.now().difference(lastPreload) < _preloadCooldown;
  }

  /// Perform the actual preload operation
  Future<void> _performPreload(String imageUrl) async {
    try {
      // Use cache manager to preload
      await _cacheManager.getSingleFile(imageUrl);
      
      // Mark as preloaded
      _preloadedUrls.add(imageUrl);
      _lastPreloadTimes[imageUrl] = DateTime.now();
      
      // Cleanup if too many preloaded URLs
      if (_preloadedUrls.length > _maxPreloadedUrls) {
        _cleanupOldPreloads();
      }
      
      LoggingService.debug('Preloaded profile picture: $imageUrl');
    } catch (e) {
      LoggingService.warning('Failed to preload profile picture: $imageUrl', e);
      rethrow;
    }
  }

  /// Clean up old preloaded URLs
  void _cleanupOldPreloads() {
    final now = DateTime.now();
    final urlsToRemove = <String>[];
    
    for (final entry in _lastPreloadTimes.entries) {
      if (now.difference(entry.value) > _preloadCooldown) {
        urlsToRemove.add(entry.key);
      }
    }
    
    for (final url in urlsToRemove) {
      _preloadedUrls.remove(url);
      _lastPreloadTimes.remove(url);
    }
    
    if (urlsToRemove.isNotEmpty) {
      LoggingService.debug('Cleaned up ${urlsToRemove.length} old preloaded URLs');
    }
  }

  /// Start background cleanup task
  void _startBackgroundCleanup() {
    Timer.periodic(const Duration(minutes: 10), (_) {
      _cleanupOldPreloads();
    });
  }

  /// Check if a profile picture is preloaded
  bool isPreloaded(String imageUrl) {
    return _preloadedUrls.contains(imageUrl) && !_isRecentlyPreloaded(imageUrl);
  }

  /// Get preload statistics
  Map<String, dynamic> getPreloadStats() {
    return {
      'preloadedUrls': _preloadedUrls.length,
      'lastPreloadTimes': _lastPreloadTimes.length,
      'activePreloads': _preloadCompleters.length,
      'maxPreloadedUrls': _maxPreloadedUrls,
      'preloadCooldown': _preloadCooldown.inMinutes,
    };
  }

  /// Clear all preloaded data
  void clearPreloadedData() {
    _preloadedUrls.clear();
    _lastPreloadTimes.clear();
    _preloadCompleters.clear();
    LoggingService.info('Cleared all preloaded profile picture data');
  }

  /// Dispose of the service
  void dispose() {
    clearPreloadedData();
    _isInitialized = false;
  }
} 