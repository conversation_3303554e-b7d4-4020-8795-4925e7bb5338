import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';

/// Performance monitoring service for profile picture loading
/// 
/// Tracks loading times, cache hit rates, and performance metrics
/// to identify bottlenecks and optimize performance.
class ProfilePicturePerformanceMonitor {
  factory ProfilePicturePerformanceMonitor() => _instance;
  ProfilePicturePerformanceMonitor._internal();
  static final ProfilePicturePerformanceMonitor _instance = ProfilePicturePerformanceMonitor._internal();

  // Performance metrics
  final Map<String, List<Duration>> _loadTimes = {};
  final Map<String, int> _cacheHits = {};
  final Map<String, int> _cacheMisses = {};
  final Map<String, int> _errors = {};
  final Map<String, DateTime> _lastLoadTimes = {};
  
  // Configuration
  static const int _maxLoadTimeHistory = 100;
  static const Duration _slowLoadThreshold = Duration(milliseconds: 1000);
  static const Duration _verySlowLoadThreshold = Duration(milliseconds: 3000);
  
  bool _isInitialized = false;

  /// Initialize the performance monitor
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Start periodic performance reporting
      _startPeriodicReporting();
      
      LoggingService.info('ProfilePicturePerformanceMonitor initialized');
      _isInitialized = true;
    } catch (e) {
      LoggingService.error('Failed to initialize ProfilePicturePerformanceMonitor', e);
    }
  }

  /// Record a profile picture load start
  String recordLoadStart(String imageUrl) {
    final loadId = '${DateTime.now().millisecondsSinceEpoch}_${imageUrl.hashCode}';
    _lastLoadTimes[loadId] = DateTime.now();
    return loadId;
  }

  /// Record a profile picture load completion
  void recordLoadComplete(String loadId, String imageUrl, {bool fromCache = false}) {
    final startTime = _lastLoadTimes[loadId];
    if (startTime == null) return;
    
    final loadTime = DateTime.now().difference(startTime);
    _recordLoadTime(imageUrl, loadTime);
    
    if (fromCache) {
      _recordCacheHit(imageUrl);
    } else {
      _recordCacheMiss(imageUrl);
    }
    
    // Log slow loads
    if (loadTime > _verySlowLoadThreshold) {
      LoggingService.warning('Very slow profile picture load: ${loadTime.inMilliseconds}ms for $imageUrl');
    } else if (loadTime > _slowLoadThreshold) {
      LoggingService.info('Slow profile picture load: ${loadTime.inMilliseconds}ms for $imageUrl');
    }
    
    _lastLoadTimes.remove(loadId);
  }

  /// Record a profile picture load error
  void recordLoadError(String loadId, String imageUrl, String error) {
    _recordError(imageUrl, error);
    _lastLoadTimes.remove(loadId);
    
    LoggingService.error('Profile picture load error: $error for $imageUrl');
  }

  /// Record load time for an image URL
  void _recordLoadTime(String imageUrl, Duration loadTime) {
    if (!_loadTimes.containsKey(imageUrl)) {
      _loadTimes[imageUrl] = [];
    }
    
    final times = _loadTimes[imageUrl]!;
    times.add(loadTime);
    
    // Keep only recent history
    if (times.length > _maxLoadTimeHistory) {
      times.removeAt(0);
    }
  }

  /// Record cache hit
  void _recordCacheHit(String imageUrl) {
    _cacheHits[imageUrl] = (_cacheHits[imageUrl] ?? 0) + 1;
  }

  /// Record cache miss
  void _recordCacheMiss(String imageUrl) {
    _cacheMisses[imageUrl] = (_cacheMisses[imageUrl] ?? 0) + 1;
  }

  /// Record error
  void _recordError(String imageUrl, String error) {
    _errors[imageUrl] = (_errors[imageUrl] ?? 0) + 1;
  }

  /// Get performance statistics for a specific image URL
  Map<String, dynamic> getImageStats(String imageUrl) {
    final loadTimes = _loadTimes[imageUrl] ?? [];
    final hits = _cacheHits[imageUrl] ?? 0;
    final misses = _cacheMisses[imageUrl] ?? 0;
    final errors = _errors[imageUrl] ?? 0;
    
    if (loadTimes.isEmpty) {
      return {
        'imageUrl': imageUrl,
        'totalLoads': 0,
        'averageLoadTime': 0,
        'minLoadTime': 0,
        'maxLoadTime': 0,
        'cacheHitRate': 0.0,
        'errorRate': 0.0,
        'slowLoads': 0,
        'verySlowLoads': 0,
      };
    }
    
    final totalLoads = loadTimes.length;
    final averageLoadTime = loadTimes.fold(0, (sum, time) => sum + time.inMilliseconds) / totalLoads;
    final minLoadTime = loadTimes.map((t) => t.inMilliseconds).reduce((a, b) => a < b ? a : b);
    final maxLoadTime = loadTimes.map((t) => t.inMilliseconds).reduce((a, b) => a > b ? a : b);
    final cacheHitRate = hits + misses > 0 ? (hits / (hits + misses)) * 100 : 0.0;
    final errorRate = totalLoads > 0 ? (errors / totalLoads) * 100 : 0.0;
    final slowLoads = loadTimes.where((t) => t > _slowLoadThreshold).length;
    final verySlowLoads = loadTimes.where((t) => t > _verySlowLoadThreshold).length;
    
    return {
      'imageUrl': imageUrl,
      'totalLoads': totalLoads,
      'averageLoadTime': averageLoadTime.round(),
      'minLoadTime': minLoadTime,
      'maxLoadTime': maxLoadTime,
      'cacheHitRate': cacheHitRate.toStringAsFixed(1),
      'errorRate': errorRate.toStringAsFixed(1),
      'slowLoads': slowLoads,
      'verySlowLoads': verySlowLoads,
    };
  }

  /// Get overall performance statistics
  Map<String, dynamic> getOverallStats() {
    final allLoadTimes = _loadTimes.values.expand((times) => times).toList();
    final totalHits = _cacheHits.values.fold(0, (sum, hits) => sum + hits);
    final totalMisses = _cacheMisses.values.fold(0, (sum, misses) => sum + misses);
    final totalErrors = _errors.values.fold(0, (sum, errors) => sum + errors);
    
    if (allLoadTimes.isEmpty) {
      return {
        'totalImages': 0,
        'totalLoads': 0,
        'averageLoadTime': 0,
        'overallCacheHitRate': 0.0,
        'overallErrorRate': 0.0,
        'slowLoads': 0,
        'verySlowLoads': 0,
        'performanceScore': 0.0,
      };
    }
    
    final totalLoads = allLoadTimes.length;
    final averageLoadTime = allLoadTimes.fold(0, (sum, time) => sum + time.inMilliseconds) / totalLoads;
    final overallCacheHitRate = totalHits + totalMisses > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0.0;
    final overallErrorRate = totalLoads > 0 ? (totalErrors / totalLoads) * 100 : 0.0;
    final slowLoads = allLoadTimes.where((t) => t > _slowLoadThreshold).length;
    final verySlowLoads = allLoadTimes.where((t) => t > _verySlowLoadThreshold).length;
    
    // Calculate performance score (0-100)
    final performanceScore = _calculatePerformanceScore(
      averageLoadTime,
      overallCacheHitRate,
      overallErrorRate,
      slowLoads,
      verySlowLoads,
      totalLoads,
    );
    
    return {
      'totalImages': _loadTimes.length,
      'totalLoads': totalLoads,
      'averageLoadTime': averageLoadTime.round(),
      'overallCacheHitRate': overallCacheHitRate.toStringAsFixed(1),
      'overallErrorRate': overallErrorRate.toStringAsFixed(1),
      'slowLoads': slowLoads,
      'verySlowLoads': verySlowLoads,
      'performanceScore': performanceScore.toStringAsFixed(1),
    };
  }

  /// Calculate performance score (0-100)
  double _calculatePerformanceScore(
    double averageLoadTime,
    double cacheHitRate,
    double errorRate,
    int slowLoads,
    int verySlowLoads,
    int totalLoads,
  ) {
    // Base score starts at 100
    double score = 100.0;
    
    // Penalize slow average load time
    if (averageLoadTime > 1000) {
      score -= (averageLoadTime - 1000) / 100; // 1 point per 100ms over 1s
    }
    
    // Penalize low cache hit rate
    score -= (100 - cacheHitRate) * 0.3; // 0.3 points per 1% cache miss
    
    // Penalize high error rate
    score -= errorRate * 2; // 2 points per 1% error rate
    
    // Penalize slow loads
    score -= (slowLoads / totalLoads) * 50; // 50 points if all loads are slow
    score -= (verySlowLoads / totalLoads) * 100; // 100 points if all loads are very slow
    
    return score.clamp(0.0, 100.0);
  }

  /// Get performance recommendations
  List<String> getPerformanceRecommendations() {
    final stats = getOverallStats();
    final recommendations = <String>[];
    
    final averageLoadTime = stats['averageLoadTime'] as int;
    final cacheHitRate = double.parse(stats['overallCacheHitRate'] as String);
    final errorRate = double.parse(stats['overallErrorRate'] as String);
    final performanceScore = double.parse(stats['performanceScore'] as String);
    
    if (averageLoadTime > 1000) {
      recommendations.add('Average load time is high (${averageLoadTime}ms). Consider optimizing image sizes or improving network connectivity.');
    }
    
    if (cacheHitRate < 70) {
      recommendations.add('Cache hit rate is low (${cacheHitRate.toStringAsFixed(1)}%). Consider implementing better caching strategies.');
    }
    
    if (errorRate > 5) {
      recommendations.add('Error rate is high (${errorRate.toStringAsFixed(1)}%). Check network connectivity and image URLs.');
    }
    
    if (performanceScore < 80) {
      recommendations.add('Overall performance score is low (${performanceScore.toStringAsFixed(1)}/100). Review all performance metrics.');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('Profile picture performance is good! No immediate optimizations needed.');
    }
    
    return recommendations;
  }

  /// Start periodic performance reporting
  void _startPeriodicReporting() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      final stats = getOverallStats();
      final performanceScore = double.parse(stats['performanceScore'] as String);
      
      if (performanceScore < 70) {
        LoggingService.warning('Profile picture performance is poor: ${performanceScore.toStringAsFixed(1)}/100');
        final recommendations = getPerformanceRecommendations();
        for (final recommendation in recommendations) {
          LoggingService.info('Performance recommendation: $recommendation');
        }
      } else if (performanceScore < 90) {
        LoggingService.info('Profile picture performance is acceptable: ${performanceScore.toStringAsFixed(1)}/100');
      } else {
        LoggingService.debug('Profile picture performance is excellent: ${performanceScore.toStringAsFixed(1)}/100');
      }
    });
  }

  /// Clear all performance data
  void clearPerformanceData() {
    _loadTimes.clear();
    _cacheHits.clear();
    _cacheMisses.clear();
    _errors.clear();
    _lastLoadTimes.clear();
    LoggingService.info('Cleared all profile picture performance data');
  }

  /// Dispose of the service
  void dispose() {
    clearPerformanceData();
    _isInitialized = false;
  }
} 