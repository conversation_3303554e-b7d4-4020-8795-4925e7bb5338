package media

import (
	"bytes"
	"context"
	"fmt"
	"image"
	_ "image/gif" // Register GIF decoder
	"image/jpeg"
	"image/png"
	_ "image/png" // Register PNG decoder
	"io"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Service handles media operations using MinIO and PostgreSQL
type Service struct {
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	minioClient *minio.Client
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
}

// Dependencies holds the dependencies for the media service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
}

// New creates a new media service instance
func New(deps *Dependencies) *Service {
	service := &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
	}

	// Initialize MinIO client
	service.initMinIOClient()

	return service
}

// RegisterRoutes registers the media service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.POST("/upload", s.authMiddleware(), s.uploadFile)
	router.POST("/confirm-upload", s.authMiddleware(), s.confirmUpload) // New endpoint for presigned uploads
	router.GET("/:fileId", s.getFile)                                   // Keep this without auth for public profile pictures
	router.HEAD("/:fileId", s.getFile)                                  // Add HEAD support for CachedNetworkImage
	router.GET("/:fileId/info", s.authMiddleware(), s.getFileInfo)
	router.DELETE("/:fileId", s.authMiddleware(), s.deleteFile)
	router.GET("/user/:userId", s.authMiddleware(), s.getUserFiles)
	router.POST("/generate-upload-url", s.authMiddleware(), s.generateUploadURL)
	router.POST("/generate-download-url", s.authMiddleware(), s.generateDownloadURL)
}

// MediaFile represents a media file in the system
type MediaFile struct {
	ID           string    `json:"id"`
	UserID       string    `json:"user_id"`
	FileName     string    `json:"file_name"`
	ContentType  string    `json:"content_type"`
	FileSize     int64     `json:"file_size"`
	BucketName   string    `json:"bucket_name"`
	ObjectKey    string    `json:"object_key"`
	URL          string    `json:"url"`
	ThumbnailURL *string   `json:"thumbnail_url,omitempty"`
	IsPublic     bool      `json:"is_public"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// UploadResponse represents the response after file upload
type UploadResponse struct {
	FileID       string  `json:"file_id"`
	URL          string  `json:"url"`
	ThumbnailURL *string `json:"thumbnail_url,omitempty"`
	FileName     string  `json:"file_name"`
	ContentType  string  `json:"content_type"`
	FileSize     int64   `json:"file_size"`
}

// GenerateURLRequest represents a request to generate a presigned URL
type GenerateURLRequest struct {
	FileName    string `json:"file_name" binding:"required"`
	ContentType string `json:"content_type" binding:"required"`
	FileSize    int64  `json:"file_size" binding:"required"`
}

// initMinIOClient initializes the MinIO client
func (s *Service) initMinIOClient() {
	var err error
	s.logger.Info("Initializing MinIO client",
		zap.String("endpoint", s.config.MinIO.Endpoint),
		zap.Bool("secure", s.config.MinIO.UseSSL),
		zap.String("region", s.config.MinIO.Region),
		zap.String("bucket", s.config.MinIO.BucketName))

	s.minioClient, err = minio.New(s.config.MinIO.Endpoint, &minio.Options{
		Creds:        credentials.NewStaticV4(s.config.MinIO.AccessKey, s.config.MinIO.SecretKey, ""),
		Secure:       s.config.MinIO.UseSSL,
		Region:       s.config.MinIO.Region,
		BucketLookup: minio.BucketLookupPath,
	})
	if err != nil {
		s.logger.Fatal("Failed to initialize MinIO client", zap.Error(err))
	}

	// Ensure bucket exists
	ctx := context.Background()
	bucketName := s.config.MinIO.BucketName
	s.logger.Info("Checking if bucket exists", zap.String("bucket", bucketName))
	exists, err := s.minioClient.BucketExists(ctx, bucketName)
	if err != nil {
		s.logger.Fatal("Failed to check bucket existence", zap.Error(err))
	}

	if !exists {
		err = s.minioClient.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{})
		if err != nil {
			s.logger.Fatal("Failed to create bucket", zap.Error(err))
		}
		s.logger.Info("Created MinIO bucket", zap.String("bucket", bucketName))
	}
}

// uploadFile handles file upload
func (s *Service) uploadFile(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// Rate limiting for file uploads
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "file_upload")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "File upload rate limit exceeded"})
		return
	}

	// Parse multipart form
	err = c.Request.ParseMultipartForm(s.config.Media.MaxFileSize)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form data"})
		return
	}

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file provided"})
		return
	}
	defer file.Close()

	// Validate file type first to determine size limits
	contentType := header.Header.Get("Content-Type")
	if !s.isAllowedContentType(contentType) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File type not allowed"})
		return
	}

	// Validate file size based on content type
	maxSize := s.config.Media.MaxFileSize
	if s.isProfilePictureUpload(contentType) {
		maxSize = s.config.Media.ProfilePictureMaxSize
	}

	if header.Size > maxSize {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("File size exceeds maximum allowed size of %d bytes", maxSize),
		})
		return
	}

	// Prepare file reader for upload
	var fileReader io.Reader
	var fileSize int64

	// Validate image dimensions for profile pictures
	if s.isImageContentType(contentType) {
		// Create a copy of the file for validation (since we need to read it twice)
		fileBytes := make([]byte, header.Size)
		_, err := io.ReadFull(file, fileBytes)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file"})
			return
		}

		// Enhanced security validation
		if err := s.validateImageSecurity(fileBytes, contentType); err != nil {
			s.logger.Warn("Image security validation failed",
				zap.String("user_id", userID.(string)),
				zap.String("content_type", contentType),
				zap.Error(err))
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Validate dimensions
		if err := s.validateImageDimensions(bytes.NewReader(fileBytes), contentType); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Strip EXIF data for security (defense-in-depth)
		cleanedBytes, err := s.stripImageMetadata(fileBytes, contentType)
		if err != nil {
			s.logger.Warn("Failed to strip image metadata, using original",
				zap.String("content_type", contentType),
				zap.Error(err))
			cleanedBytes = fileBytes // Fallback to original if stripping fails
		}

		// Create a new file reader for upload with cleaned image
		fileReader = bytes.NewReader(cleanedBytes)
		fileSize = int64(len(cleanedBytes))
	} else {
		// For non-image files, use the original file reader
		fileReader = file
		fileSize = header.Size
	}

	// Generate unique file ID and object key
	fileID := uuid.New().String()
	fileExtension := filepath.Ext(header.Filename)
	objectKey := fmt.Sprintf("%s/%s%s", userID.(string), fileID, fileExtension)

	// Upload to MinIO
	_, err = s.minioClient.PutObject(
		c.Request.Context(),
		s.config.MinIO.BucketName,
		objectKey,
		fileReader,
		fileSize,
		minio.PutObjectOptions{
			ContentType: contentType,
		},
	)
	if err != nil {
		s.logger.Error("Failed to upload file to MinIO", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload file"})
		return
	}

	// Generate file URL - use backend API URL instead of direct MinIO URL
	fileURL := s.generateBackendFileURL(fileID)

	// Generate thumbnail for images
	var thumbnailURL *string
	if s.isImageContentType(contentType) {
		thumbnail := s.generateBackendFileURL(fileID) // Use same URL for now, thumbnail logic can be added later
		thumbnailURL = &thumbnail
	}

	// Determine if file should be public (profile pictures should be public)
	isPublic := s.isImageContentType(contentType) // Make all images public for now

	// Save file metadata to database
	mediaFile := &MediaFile{
		ID:           fileID,
		UserID:       userID.(string),
		FileName:     header.Filename,
		ContentType:  contentType,
		FileSize:     header.Size,
		BucketName:   s.config.MinIO.BucketName,
		ObjectKey:    objectKey,
		URL:          fileURL,
		ThumbnailURL: thumbnailURL,
		IsPublic:     isPublic, // Set to true for images (profile pictures)
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.saveFileMetadata(c.Request.Context(), mediaFile); err != nil {
		s.logger.Error("Failed to save file metadata", zap.Error(err))
		// Clean up uploaded file
		s.minioClient.RemoveObject(c.Request.Context(), s.config.MinIO.BucketName, objectKey, minio.RemoveObjectOptions{})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file metadata"})
		return
	}

	s.logger.Info("File uploaded successfully",
		zap.String("file_id", fileID),
		zap.String("user_id", userID.(string)),
		zap.String("file_name", header.Filename))

	response := &UploadResponse{
		FileID:       fileID,
		URL:          fileURL,
		ThumbnailURL: thumbnailURL,
		FileName:     header.Filename,
		ContentType:  contentType,
		FileSize:     header.Size,
	}

	c.JSON(http.StatusCreated, response)
}

// getFile handles file download/serving with enhanced caching
func (s *Service) getFile(c *gin.Context) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File ID is required"})
		return
	}

	// Get file metadata from database
	mediaFile, err := s.getFileMetadata(c.Request.Context(), fileID)
	if err != nil {
		s.logger.Error("Failed to get file metadata", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// Check if file is public (profile pictures should be public)
	if !mediaFile.IsPublic {
		// For private files, require authentication
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			return
		}

		// Check if user owns the file
		if mediaFile.UserID != userID.(string) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
	}

	// Get object from MinIO
	obj, err := s.minioClient.GetObject(
		c.Request.Context(),
		s.config.MinIO.BucketName,
		mediaFile.ObjectKey,
		minio.GetObjectOptions{},
	)
	if err != nil {
		s.logger.Error("Failed to get object from MinIO", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}
	defer obj.Close()

	// Get object info for content length
	objInfo, err := obj.Stat()
	if err != nil {
		s.logger.Error("Failed to get object info", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file info"})
		return
	}

	// Set optimized headers for profile pictures
	s._setOptimizedHeaders(c, mediaFile, objInfo)

	// Handle HEAD requests (for CachedNetworkImage)
	if c.Request.Method == "HEAD" {
		c.Status(http.StatusOK)
		return
	}

	// Stream the file with optimized settings
	c.DataFromReader(
		http.StatusOK,
		objInfo.Size,
		objInfo.ContentType,
		obj,
		map[string]string{
			"Content-Disposition": fmt.Sprintf("inline; filename=\"%s\"", mediaFile.FileName),
		},
	)
}

// _setOptimizedHeaders sets optimized cache headers for profile pictures
func (s *Service) _setOptimizedHeaders(c *gin.Context, mediaFile *MediaFile, objInfo minio.ObjectInfo) {
	// Set content type
	c.Header("Content-Type", mediaFile.ContentType)

	// Set content length
	c.Header("Content-Length", fmt.Sprintf("%d", objInfo.Size))

	// Optimized cache headers for profile pictures
	if s.isImageContentType(mediaFile.ContentType) {
		// Profile pictures: long-term caching (1 year)
		c.Header("Cache-Control", "public, max-age=31536000, immutable")
		c.Header("ETag", fmt.Sprintf("\"%s\"", objInfo.ETag))
		c.Header("Last-Modified", objInfo.LastModified.Format(http.TimeFormat))

		// Enable compression
		c.Header("Accept-Encoding", "gzip, deflate, br")

		// Security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")

		// CORS headers for profile pictures
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, HEAD")
		c.Header("Access-Control-Allow-Headers", "Range, If-None-Match, If-Modified-Since")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Range, ETag, Last-Modified")
	} else {
		// Other files: shorter cache (1 day)
		c.Header("Cache-Control", "public, max-age=86400")
		c.Header("ETag", fmt.Sprintf("\"%s\"", objInfo.ETag))
	}

	// Handle conditional requests (If-None-Match, If-Modified-Since)
	if s._handleConditionalRequest(c, objInfo) {
		return // Response already sent
	}
}

// _handleConditionalRequest handles If-None-Match and If-Modified-Since headers
func (s *Service) _handleConditionalRequest(c *gin.Context, objInfo minio.ObjectInfo) bool {
	// Check If-None-Match
	if ifNoneMatch := c.GetHeader("If-None-Match"); ifNoneMatch != "" {
		if ifNoneMatch == fmt.Sprintf("\"%s\"", objInfo.ETag) {
			c.Status(http.StatusNotModified)
			return true
		}
	}

	// Check If-Modified-Since
	if ifModifiedSince := c.GetHeader("If-Modified-Since"); ifModifiedSince != "" {
		if modifiedSince, err := http.ParseTime(ifModifiedSince); err == nil {
			if !objInfo.LastModified.After(modifiedSince) {
				c.Status(http.StatusNotModified)
				return true
			}
		}
	}

	return false
}

// getFileInfo handles getting file metadata
func (s *Service) getFileInfo(c *gin.Context) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File ID is required"})
		return
	}

	userID, _ := c.Get("user_id")

	// Get file metadata from database
	mediaFile, err := s.getFileMetadata(c.Request.Context(), fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// Check if user has access
	if !mediaFile.IsPublic && userID.(string) != mediaFile.UserID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	c.JSON(http.StatusOK, mediaFile)
}

// deleteFile handles file deletion
func (s *Service) deleteFile(c *gin.Context) {
	fileID := c.Param("fileId")
	if fileID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File ID is required"})
		return
	}

	userID, _ := c.Get("user_id")

	// Get file metadata from database
	mediaFile, err := s.getFileMetadata(c.Request.Context(), fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// Check if user owns the file
	if userID.(string) != mediaFile.UserID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only delete your own files"})
		return
	}

	// Delete from MinIO
	err = s.minioClient.RemoveObject(
		c.Request.Context(),
		mediaFile.BucketName,
		mediaFile.ObjectKey,
		minio.RemoveObjectOptions{},
	)
	if err != nil {
		s.logger.Error("Failed to delete file from MinIO", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete file"})
		return
	}

	// Delete metadata from database
	if err := s.deleteFileMetadata(c.Request.Context(), fileID); err != nil {
		s.logger.Error("Failed to delete file metadata", zap.Error(err))
		// File is already deleted from MinIO, but metadata remains
		// This is acceptable as metadata cleanup can be done later
	}

	s.logger.Info("File deleted successfully",
		zap.String("file_id", fileID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "File deleted successfully"})
}

// getUserFiles handles getting user's files
func (s *Service) getUserFiles(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Param("userId")

	// Users can only see their own files unless files are public
	if userID.(string) != targetUserID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only access your own files"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	contentType := c.Query("content_type")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// Build query
	query := `
		SELECT id, user_id, file_name, content_type, file_size, bucket_name,
			   object_key, url, thumbnail_url, is_public, created_at, updated_at
		FROM media_files
		WHERE user_id = $1`
	args := []interface{}{targetUserID}
	argIndex := 2

	if contentType != "" {
		query += fmt.Sprintf(" AND content_type LIKE $%d", argIndex)
		args = append(args, contentType+"%")
		argIndex++
	}

	query += " ORDER BY created_at DESC"
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, pageSize, offset)

	rows, err := s.db.Pool.Query(c.Request.Context(), query, args...)
	if err != nil {
		s.logger.Error("Failed to get user files", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files"})
		return
	}
	defer rows.Close()

	var files []*MediaFile
	for rows.Next() {
		var file MediaFile
		err := rows.Scan(
			&file.ID, &file.UserID, &file.FileName, &file.ContentType,
			&file.FileSize, &file.BucketName, &file.ObjectKey, &file.URL,
			&file.ThumbnailURL, &file.IsPublic, &file.CreatedAt, &file.UpdatedAt,
		)
		if err != nil {
			s.logger.Error("Failed to scan file", zap.Error(err))
			continue
		}
		files = append(files, &file)
	}

	// Get total count
	countQuery := `SELECT COUNT(*) FROM media_files WHERE user_id = $1`
	countArgs := []interface{}{targetUserID}

	if contentType != "" {
		countQuery += " AND content_type LIKE $2"
		countArgs = append(countArgs, contentType+"%")
	}

	var totalCount int
	err = s.db.Pool.QueryRow(c.Request.Context(), countQuery, countArgs...).Scan(&totalCount)
	if err != nil {
		s.logger.Error("Failed to get file count", zap.Error(err))
		totalCount = len(files)
	}

	c.JSON(http.StatusOK, gin.H{
		"files": files,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total_count": totalCount,
			"total_pages": (totalCount + pageSize - 1) / pageSize,
		},
	})
}

// generateUploadURL handles generating presigned upload URLs
func (s *Service) generateUploadURL(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req GenerateURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate file size
	if req.FileSize > s.config.Media.MaxFileSize {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size exceeds limit"})
		return
	}

	// Validate content type
	if !s.isAllowedContentType(req.ContentType) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File type not allowed"})
		return
	}

	// Generate unique object key
	fileID := uuid.New().String()
	fileExtension := filepath.Ext(req.FileName)
	objectKey := fmt.Sprintf("%s/%s%s", userID.(string), fileID, fileExtension)

	// Generate presigned upload URL
	presignedURL, err := s.minioClient.PresignedPutObject(
		c.Request.Context(),
		s.config.MinIO.BucketName,
		objectKey,
		time.Hour, // URL expires in 1 hour
	)
	if err != nil {
		s.logger.Error("Failed to generate presigned URL", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate upload URL"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"upload_url": presignedURL.String(),
		"file_id":    fileID,
		"object_key": objectKey,
		"expires_in": 3600, // 1 hour in seconds
	})
}

// generateDownloadURL handles generating presigned download URLs
func (s *Service) generateDownloadURL(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		FileID string `json:"file_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get file metadata
	mediaFile, err := s.getFileMetadata(c.Request.Context(), req.FileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// Check if user has access
	if !mediaFile.IsPublic && userID.(string) != mediaFile.UserID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Generate presigned download URL
	presignedURL, err := s.minioClient.PresignedGetObject(
		c.Request.Context(),
		mediaFile.BucketName,
		mediaFile.ObjectKey,
		time.Hour, // URL expires in 1 hour
		nil,
	)
	if err != nil {
		s.logger.Error("Failed to generate presigned download URL", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate download URL"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"download_url": presignedURL.String(),
		"expires_in":   3600, // 1 hour in seconds
	})
}

// confirmUpload handles confirming a presigned upload and saving metadata
func (s *Service) confirmUpload(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		FileID      string `json:"file_id" binding:"required"`
		FileName    string `json:"file_name" binding:"required"`
		ContentType string `json:"content_type" binding:"required"`
		FileSize    int64  `json:"file_size" binding:"required"`
		ObjectKey   string `json:"object_key" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate content type first
	if !s.isAllowedContentType(req.ContentType) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File type not allowed"})
		return
	}

	// Validate file size based on content type
	maxSize := s.config.Media.MaxFileSize
	if s.isProfilePictureUpload(req.ContentType) {
		maxSize = s.config.Media.ProfilePictureMaxSize
	}

	if req.FileSize > maxSize {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("File size exceeds maximum allowed size of %d bytes", maxSize),
		})
		return
	}

	// Verify the object exists in MinIO
	_, err := s.minioClient.StatObject(
		c.Request.Context(),
		s.config.MinIO.BucketName,
		req.ObjectKey,
		minio.StatObjectOptions{},
	)
	if err != nil {
		s.logger.Error("File not found in MinIO after presigned upload",
			zap.String("object_key", req.ObjectKey),
			zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found in storage"})
		return
	}

	// Generate file URL - use backend API URL
	fileURL := s.generateBackendFileURL(req.FileID)

	// Generate thumbnail for images
	var thumbnailURL *string
	if s.isImageContentType(req.ContentType) {
		thumbnail := s.generateBackendFileURL(req.FileID) // Use same URL for now
		thumbnailURL = &thumbnail
	}

	// Determine if file should be public (profile pictures should be public)
	isPublic := s.isImageContentType(req.ContentType)

	// Save file metadata to database
	mediaFile := &MediaFile{
		ID:           req.FileID,
		UserID:       userID.(string),
		FileName:     req.FileName,
		ContentType:  req.ContentType,
		FileSize:     req.FileSize,
		BucketName:   s.config.MinIO.BucketName,
		ObjectKey:    req.ObjectKey,
		URL:          fileURL,
		ThumbnailURL: thumbnailURL,
		IsPublic:     isPublic,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.saveFileMetadata(c.Request.Context(), mediaFile); err != nil {
		s.logger.Error("Failed to save file metadata after presigned upload", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file metadata"})
		return
	}

	s.logger.Info("File metadata saved successfully after presigned upload",
		zap.String("file_id", req.FileID),
		zap.String("user_id", userID.(string)),
		zap.String("file_name", req.FileName))

	response := &UploadResponse{
		FileID:       req.FileID,
		URL:          fileURL,
		ThumbnailURL: thumbnailURL,
		FileName:     req.FileName,
		ContentType:  req.ContentType,
		FileSize:     req.FileSize,
	}

	c.JSON(http.StatusCreated, response)
}

// Helper methods

// saveFileMetadata saves file metadata to PostgreSQL
func (s *Service) saveFileMetadata(ctx context.Context, file *MediaFile) error {
	query := `
		INSERT INTO media_files (id, user_id, file_name, content_type, file_size,
								bucket_name, object_key, url, thumbnail_url, is_public,
								created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`

	_, err := s.db.Pool.Exec(ctx, query,
		file.ID, file.UserID, file.FileName, file.ContentType, file.FileSize,
		file.BucketName, file.ObjectKey, file.URL, file.ThumbnailURL, file.IsPublic,
		file.CreatedAt, file.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to save file metadata: %w", err)
	}

	return nil
}

// getFileMetadata retrieves file metadata from PostgreSQL
func (s *Service) getFileMetadata(ctx context.Context, fileID string) (*MediaFile, error) {
	query := `
		SELECT id, user_id, file_name, content_type, file_size, bucket_name,
			   object_key, url, thumbnail_url, is_public, created_at, updated_at
		FROM media_files
		WHERE id = $1`

	var file MediaFile
	err := s.db.Pool.QueryRow(ctx, query, fileID).Scan(
		&file.ID, &file.UserID, &file.FileName, &file.ContentType,
		&file.FileSize, &file.BucketName, &file.ObjectKey, &file.URL,
		&file.ThumbnailURL, &file.IsPublic, &file.CreatedAt, &file.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get file metadata: %w", err)
	}

	return &file, nil
}

// deleteFileMetadata deletes file metadata from PostgreSQL
func (s *Service) deleteFileMetadata(ctx context.Context, fileID string) error {
	query := `DELETE FROM media_files WHERE id = $1`

	_, err := s.db.Pool.Exec(ctx, query, fileID)
	if err != nil {
		return fmt.Errorf("failed to delete file metadata: %w", err)
	}

	return nil
}

// generateBackendFileURL generates the backend API URL for a file
func (s *Service) generateBackendFileURL(fileID string) string {
	// Use the backend API URL instead of direct MinIO URL
	return fmt.Sprintf("https://10.0.0.81:4000/api/v1/media/%s", fileID)
}

// generateCDNURL generates a CDN URL if CDN is enabled, otherwise returns backend URL
func (s *Service) generateCDNURL(objectKey string) string {
	if s.config.Media.CDN.Enabled {
		return fmt.Sprintf("%s/%s", s.config.Media.CDN.BaseURL, objectKey)
	}
	// Fallback to direct MinIO URL if CDN is not enabled
	return fmt.Sprintf("http://%s/%s/%s", s.config.MinIO.Endpoint, s.config.MinIO.BucketName, objectKey)
}

// generateOptimizedFileURL generates the best URL for file access (CDN if available, otherwise backend)
func (s *Service) generateOptimizedFileURL(fileID string, objectKey string) string {
	if s.config.Media.CDN.Enabled {
		return s.generateCDNURL(objectKey)
	}
	return s.generateBackendFileURL(fileID)
}

// generateFileURL generates the public URL for a file (legacy method, kept for compatibility)
func (s *Service) generateFileURL(objectKey string) string {
	endpoint := s.config.MinIO.Endpoint
	// If no port specified, default to 9000
	if !strings.Contains(endpoint, ":") {
		endpoint = endpoint + ":9000"
	}
	scheme := "http"
	if s.config.MinIO.UseSSL {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s/%s/%s", scheme, endpoint, s.config.MinIO.BucketName, objectKey)
}

// generateThumbnailURL generates thumbnail URL for images
func (s *Service) generateThumbnailURL(objectKey string) string {
	// Generate thumbnail object key
	thumbnailKey := s.generateThumbnailKey(objectKey)

	// Check if thumbnail exists, if not create it
	if !s.thumbnailExists(thumbnailKey) {
		if err := s.createThumbnail(objectKey, thumbnailKey); err != nil {
			s.logger.Error("Failed to create thumbnail, returning original",
				zap.String("object_key", objectKey),
				zap.Error(err))
			return s.generateFileURL(objectKey)
		}
	}

	return s.generateFileURL(thumbnailKey)
}

// generateThumbnailKey generates the object key for thumbnail
func (s *Service) generateThumbnailKey(originalKey string) string {
	// Extract file extension and replace with thumbnail suffix
	lastDot := strings.LastIndex(originalKey, ".")
	if lastDot == -1 {
		return originalKey + "_thumb.jpg"
	}

	base := originalKey[:lastDot]
	return base + "_thumb.jpg"
}

// thumbnailExists checks if thumbnail already exists in MinIO
func (s *Service) thumbnailExists(thumbnailKey string) bool {
	ctx := context.Background()
	_, err := s.minioClient.StatObject(ctx, s.config.MinIO.BucketName, thumbnailKey, minio.StatObjectOptions{})
	return err == nil
}

// createThumbnail creates a thumbnail from the original image
func (s *Service) createThumbnail(originalKey, thumbnailKey string) error {
	ctx := context.Background()

	// Get original image from MinIO
	object, err := s.minioClient.GetObject(ctx, s.config.MinIO.BucketName, originalKey, minio.GetObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to get original image: %w", err)
	}
	defer object.Close()

	// Decode image
	img, _, err := image.Decode(object)
	if err != nil {
		return fmt.Errorf("failed to decode image: %w", err)
	}

	// Create thumbnail (resize to max 300x300 while maintaining aspect ratio)
	thumbnail := s.resizeImage(img, 300, 300)

	// Encode thumbnail as JPEG
	var buf bytes.Buffer
	if err := jpeg.Encode(&buf, thumbnail, &jpeg.Options{Quality: 85}); err != nil {
		return fmt.Errorf("failed to encode thumbnail: %w", err)
	}

	// Upload thumbnail to MinIO
	_, err = s.minioClient.PutObject(ctx, s.config.MinIO.BucketName, thumbnailKey,
		bytes.NewReader(buf.Bytes()), int64(buf.Len()), minio.PutObjectOptions{
			ContentType: "image/jpeg",
		})

	if err != nil {
		return fmt.Errorf("failed to upload thumbnail: %w", err)
	}

	s.logger.Info("Thumbnail created successfully",
		zap.String("original_key", originalKey),
		zap.String("thumbnail_key", thumbnailKey))

	return nil
}

// resizeImage resizes an image while maintaining aspect ratio
func (s *Service) resizeImage(img image.Image, maxWidth, maxHeight int) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// Calculate new dimensions while maintaining aspect ratio
	ratio := float64(width) / float64(height)
	newWidth := maxWidth
	newHeight := int(float64(newWidth) / ratio)

	if newHeight > maxHeight {
		newHeight = maxHeight
		newWidth = int(float64(newHeight) * ratio)
	}

	// Create new image with calculated dimensions
	newImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// Simple nearest neighbor scaling (for production, use a better algorithm)
	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			srcX := x * width / newWidth
			srcY := y * height / newHeight
			newImg.Set(x, y, img.At(srcX, srcY))
		}
	}

	return newImg
}

// isAllowedContentType checks if the content type is allowed
func (s *Service) isAllowedContentType(contentType string) bool {
	allowedTypes := []string{
		"image/jpeg", "image/png", "image/gif", "image/webp",
		"video/mp4", "video/webm", "video/quicktime",
		"audio/mpeg", "audio/wav", "audio/ogg",
		"application/pdf", "text/plain",
		"application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	}

	for _, allowed := range allowedTypes {
		if contentType == allowed {
			return true
		}
	}

	return false
}

// isImageContentType checks if the content type is an image
func (s *Service) isImageContentType(contentType string) bool {
	return strings.HasPrefix(contentType, "image/")
}

// isAvifContentType checks if the content type is AVIF
func (s *Service) isAvifContentType(contentType string) bool {
	return contentType == "image/avif"
}

// isProfilePictureUpload checks if the content type is allowed for profile pictures
func (s *Service) isProfilePictureUpload(contentType string) bool {
	for _, allowedType := range s.config.Media.ProfilePictureTypes {
		if contentType == allowedType {
			return true
		}
	}
	return false
}

// validateImageDimensions validates image dimensions for profile pictures
func (s *Service) validateImageDimensions(file io.Reader, contentType string) error {
	if !s.isProfilePictureUpload(contentType) {
		return nil // Only validate profile pictures
	}

	// AVIF files require special handling since Go's image package doesn't support them
	if s.isAvifContentType(contentType) {
		// For AVIF files, we trust the frontend validation
		// In production, you might want to use a library like libavif-go
		s.logger.Info("AVIF file detected, skipping dimension validation",
			zap.String("content_type", contentType))
		return nil
	}

	// Decode image to check dimensions for supported formats
	img, _, err := image.Decode(file)
	if err != nil {
		return fmt.Errorf("invalid image format: %w", err)
	}

	const maxResolution = 8192 // Allow images up to 8192x8192
	const minResolution = 640  // Minimum resolution requirement
	width := img.Bounds().Dx()
	height := img.Bounds().Dy()

	// Check minimum resolution
	if width < minResolution || height < minResolution {
		return fmt.Errorf("image resolution %dx%d is below minimum %dx%d pixels",
			width, height, minResolution, minResolution)
	}

	// Check maximum resolution (prevent extremely large files)
	if width > maxResolution || height > maxResolution {
		return fmt.Errorf("image resolution %dx%d exceeds maximum %dx%d pixels",
			width, height, maxResolution, maxResolution)
	}

	s.logger.Info("Image dimensions validated",
		zap.Int("width", width),
		zap.Int("height", height),
		zap.String("content_type", contentType))

	return nil
}

// validateImageSecurity performs advanced security validation on image files
func (s *Service) validateImageSecurity(fileBytes []byte, contentType string) error {
	// 1. Validate file signature (magic bytes)
	if err := s.validateFileSignature(fileBytes, contentType); err != nil {
		return fmt.Errorf("file signature validation failed: %w", err)
	}

	// 2. Check for suspicious patterns in file content
	if err := s.scanForMaliciousPatterns(fileBytes); err != nil {
		return fmt.Errorf("malicious pattern detected: %w", err)
	}

	// 3. Validate image structure integrity
	if err := s.validateImageStructure(fileBytes, contentType); err != nil {
		return fmt.Errorf("image structure validation failed: %w", err)
	}

	return nil
}

// validateFileSignature checks if file magic bytes match the declared content type
func (s *Service) validateFileSignature(fileBytes []byte, contentType string) error {
	if len(fileBytes) < 12 {
		return fmt.Errorf("file too small for signature validation")
	}

	// Define expected magic bytes for each content type
	signatures := map[string][]byte{
		"image/jpeg": {0xFF, 0xD8, 0xFF},
		"image/png":  {0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A},
		"image/gif":  {0x47, 0x49, 0x46, 0x38},
		"image/webp": {0x52, 0x49, 0x46, 0x46}, // RIFF header (WebP is RIFF-based)
	}

	expectedSig, exists := signatures[contentType]
	if !exists {
		// For unknown types, skip signature validation
		return nil
	}

	// Check if file starts with expected signature
	if len(fileBytes) < len(expectedSig) {
		return fmt.Errorf("file too small for %s signature", contentType)
	}

	for i, expectedByte := range expectedSig {
		if fileBytes[i] != expectedByte {
			return fmt.Errorf("file signature mismatch: expected %s but got different signature", contentType)
		}
	}

	// Additional WebP validation (check for WEBP identifier)
	if contentType == "image/webp" && len(fileBytes) >= 12 {
		webpSig := []byte{0x57, 0x45, 0x42, 0x50} // "WEBP"
		for i, expectedByte := range webpSig {
			if fileBytes[8+i] != expectedByte {
				return fmt.Errorf("invalid WebP format: missing WEBP identifier")
			}
		}
	}

	return nil
}

// scanForMaliciousPatterns scans file content for known malicious patterns
func (s *Service) scanForMaliciousPatterns(fileBytes []byte) error {
	// Convert to lowercase for case-insensitive scanning
	content := strings.ToLower(string(fileBytes))

	// Define suspicious patterns that shouldn't appear in image files
	maliciousPatterns := []string{
		"<script",
		"javascript:",
		"vbscript:",
		"data:text/html",
		"<?php",
		"<%",
		"eval(",
		"document.cookie",
		"window.location",
		"xmlhttprequest",
		"fetch(",
		"import(",
		"require(",
		"process.env",
		"fs.readfile",
		"exec(",
		"system(",
		"shell_exec",
		"passthru",
		"file_get_contents",
		"fopen(",
		"fwrite(",
		"include(",
		"require_once",
	}

	for _, pattern := range maliciousPatterns {
		if strings.Contains(content, pattern) {
			return fmt.Errorf("suspicious pattern detected: %s", pattern)
		}
	}

	return nil
}

// validateImageStructure validates the internal structure of image files
func (s *Service) validateImageStructure(fileBytes []byte, contentType string) error {
	// Try to decode the image to ensure it's structurally valid
	_, format, err := image.Decode(bytes.NewReader(fileBytes))
	if err != nil {
		return fmt.Errorf("invalid image structure: %w", err)
	}

	// Verify format matches content type
	expectedFormats := map[string]string{
		"image/jpeg": "jpeg",
		"image/png":  "png",
		"image/gif":  "gif",
	}

	if expectedFormat, exists := expectedFormats[contentType]; exists {
		if format != expectedFormat {
			return fmt.Errorf("format mismatch: content-type is %s but file format is %s", contentType, format)
		}
	}

	return nil
}

// stripImageMetadata removes EXIF and other metadata from images for security
func (s *Service) stripImageMetadata(imageBytes []byte, contentType string) ([]byte, error) {
	// Only process JPEG and PNG images (WebP doesn't typically contain EXIF)
	if contentType != "image/jpeg" && contentType != "image/png" {
		return imageBytes, nil // Return original for other formats
	}

	// Decode the image
	img, format, err := image.Decode(bytes.NewReader(imageBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Re-encode the image without metadata
	var buf bytes.Buffer
	switch format {
	case "jpeg":
		// Re-encode as JPEG without EXIF data
		err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: 95})
	case "png":
		// Re-encode as PNG (PNG doesn't typically have EXIF, but this ensures clean encoding)
		err = png.Encode(&buf, img)
	default:
		// For other formats, return original
		return imageBytes, nil
	}

	if err != nil {
		return nil, fmt.Errorf("failed to re-encode image: %w", err)
	}

	s.logger.Debug("Successfully stripped image metadata",
		zap.String("format", format),
		zap.Int("original_size", len(imageBytes)),
		zap.Int("cleaned_size", buf.Len()))

	return buf.Bytes(), nil
}

// handleConditionalRequest handles If-None-Match and If-Modified-Since headers
// Returns true if a 304 Not Modified response was sent
func (s *Service) handleConditionalRequest(c *gin.Context, file *MediaFile, etag string) bool {
	// Check If-None-Match header (ETag-based conditional request)
	if match := c.GetHeader("If-None-Match"); match != "" {
		if match == etag || match == "*" {
			s.logger.Debug("ETag match found, returning 304 Not Modified",
				zap.String("file_id", file.ID),
				zap.String("etag", etag))
			c.Status(http.StatusNotModified)
			return true
		}
	}

	// Check If-Modified-Since header (time-based conditional request)
	if since := c.GetHeader("If-Modified-Since"); since != "" {
		if t, err := time.Parse(http.TimeFormat, since); err == nil {
			if !file.UpdatedAt.After(t) {
				s.logger.Debug("File not modified since request time, returning 304 Not Modified",
					zap.String("file_id", file.ID),
					zap.Time("file_updated", file.UpdatedAt),
					zap.Time("if_modified_since", t))
				c.Status(http.StatusNotModified)
				return true
			}
		}
	}

	return false
}

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.AuthMiddleware(s.oryClient, s.logger)
}
