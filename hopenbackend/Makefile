# Hopen Backend Makefile

# ---------------------------------------------------------------------------
# Infrastructure
# ---------------------------------------------------------------------------
COMPOSE=docker-compose

infra-up:
	$(COMPOSE) up -d --build --remove-orphans
	$(MAKE) migrate

infra-down:
	$(COMPOSE) down -v

# ---------------------------------------------------------------------------
# Database seeding (placeholder – extend with real seeders)
# ---------------------------------------------------------------------------
seed-db:
	@echo "Running database seeders (stub)"
	go run scripts/seed/main.go || true

# ---------------------------------------------------------------------------
# Performance Tests (k6)
# ---------------------------------------------------------------------------
K6 ?= k6
K6_FILE=tests/load/k6_scenario.js
K6_FLAGS=--vus 5000 --duration 5m

k6-steady:
	$(K6) run $(K6_FLAGS) $(K6_FILE)

k6-spike:
	$(K6) run --executor spike $(K6_FILE)

# ---------------------------------------------------------------------------
# JetStream test
# ---------------------------------------------------------------------------
jetstream-test:
	go test ./tests/performance -run TestJetStream*

# ---------------------------------------------------------------------------
# Static analysis & linting
# ---------------------------------------------------------------------------
lint:
	golangci-lint run ./...

vet:
	go vet ./...

# ---------------------------------------------------------------------------
# Comprehensive CI test (minus GitHub Action wrapper)
# ---------------------------------------------------------------------------
ci-test: infra-up seed-db vet lint test-all

# ---------------------------------------------------------------------------
# Database Migrations (using golang-migrate)
# ---------------------------------------------------------------------------

POSTGRES_CONTAINER=hopen_postgresql
POSTGRES_URL=postgres://hopen:hopen123@localhost:5432/hopen?sslmode=disable
MIGRATIONS_PATH=migrations/postgresql

# Install golang-migrate CLI tool
install-migrate:
	@echo "Installing golang-migrate CLI tool..."
	@which migrate >/dev/null || (echo "Installing migrate..." && \
		curl -L https://github.com/golang-migrate/migrate/releases/latest/download/migrate.darwin-amd64.tar.gz | tar xvz && \
		sudo mv migrate /usr/local/bin/)

# Create a new migration file
migrate-create:
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir $(MIGRATIONS_PATH) -seq $$name

# Run all pending migrations
migrate-up: install-migrate
	@echo "Running database migrations..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" up

# Rollback the last migration
migrate-down: install-migrate
	@echo "Rolling back last migration..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" down 1

# Rollback all migrations
migrate-reset: install-migrate
	@echo "Rolling back all migrations..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" down

# Check migration status
migrate-status: install-migrate
	@echo "Checking migration status..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" version

# Force migration version (use with caution)
migrate-force: install-migrate
	@read -p "Enter version to force: " version; \
	migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" force $$version

# Legacy migrate target for backward compatibility
migrate: migrate-up

# ---------------------------------------------------------------------------
# Linkerd CLI tooling
# ---------------------------------------------------------------------------

linkerd-install:
	@echo "Installing Linkerd CLI …"
	@curl -sL https://run.linkerd.io/install | sh -- --stable --skip-verify >/dev/null 2>&1
	@mkdir -p ./bin
	@cp $$HOME/.linkerd2/bin/linkerd ./bin/ || true

linkerd-check: linkerd-install
	PATH="./bin:$$PATH" bash scripts/check_linkerd_mtls.sh

# ---------------------------------------------------------------------------
# Clean build artifacts and docker junk
# ---------------------------------------------------------------------------

clean:
	@echo "Cleaning Go build cache …"
	go clean -cache
	@echo "Cleaning Go module cache …"
	go clean -modcache
	@echo "Cleaning Go test cache …"
	go clean -testcache
	@echo "Removing compiled binaries (if any) …"
	rm -f hopenbackend || true
	@echo "Removing Go binaries from bin directory …"
	rm -f bin/* || true
	@echo "Pruning unused Docker images, containers, volumes, and networks …"
	docker system prune -f --volumes
	@echo "Cleanup complete!"

.PHONY: infra-up infra-down seed-db k6-steady k6-spike jetstream-test lint vet ci-test migrate migrate-up migrate-down migrate-reset migrate-status migrate-force migrate-create install-migrate linkerd-install linkerd-check clean